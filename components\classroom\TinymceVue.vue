<template>
  <classroom-container
    v-slot="scope"
    :asset="file"
    :lock-aspect-ratio="false"
    :is-draggable-prop="isDraggableProp"
    hide-resize-icon
  >
    <div class="classroom-editor">
      <editor
        :id="frameElId"
        v-model="text"
        :api-key="apiKey"
        :init="{
          language: locale,
          height: '100%',
          placeholder: $t('welcome_to_new_langu_classroom'),
          plugins: ['lists link hr table codesample autolink'],
          extended_valid_elements: 'a[href|target=_blank]',
          menubar: '',
          branding: false,
          contextmenu: false,
          browser_spellcheck: true,
          toolbar_drawer: 'wrap',
          mobile: {
            toolbar_drawer: 'wrap',
          },
          toolbar,
          setup: (_editor) => {
            _editor.ui.registry.addMenuButton('exportButton', {
              icon: 'export',
              tooltip: 'Export',
              fetch(callback) {
                const items = [
                  {
                    type: 'menuitem',
                    text: 'PDF',
                    tooltip: 'PDF',
                    onAction() {
                      generatePdf()
                    },
                  },
                ]

                callback(items)
              },
            })
          },
          content_style,
        }"
        :disabled="!isSocketConnected"
        @onInit="initHandler"
        @onClick="setStartCursorPos"
        @onFocus="focusHandler(scope)"
        @onKeyUp="keyUpHandler"
        @onKeyDown="keyDownHandler"
        @onMouseMove="mouseOverHandler"
        @onDragOver="dragOverHandler"
      ></editor>
    </div>
  </classroom-container>
</template>

<script>
import Editor from '@tinymce/tinymce-vue'
import { debounce } from '~/helpers'

import ClassroomContainer from '~/components/classroom/ClassroomContainer'

export default {
  name: 'TinymceVue',
  components: { ClassroomContainer, Editor },
  props: {
    file: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      frameElId: 'tinymce_editor',
      apiKey: process.env.NUXT_ENV_TINYMCE_KEY,
      editor: null,
      text: '',
      toolbar: [
        'bold italic strikethrough underline',
        'forecolor backcolor',
        'link hr table',
        'alignleft aligncenter alignright alignjustify',
        'bullist numlist outdent indent',
        'exportButton codesample',
      ].join(' | '),
      content_style: `
        html {
          height: 100%;
          cursor: text;
        }

        body {
          height: calc(100% - 30px);
          margin: 15px;
        }

        .mce-content-body[data-mce-placeholder]::before {
          cursor: text;
        }

        body p {
          margin: 15px 0;
        }
      `,
      frameEl: null,
      frameEditAreaEl: null,
      frameDoc: null,
      changedBySocket: false,
      changedByDatabase: true,
      startCursorPos: null,
      endCursorPos: null,
      pressButtonKey: null,
      currentNode: null,
      previousNode: null,
      offset: null,
      isCaretPositionFound: false,
      isDraggableProp: true,
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    isSocketConnected() {
      return this.$store.state.socket.isConnected
    },
    isCtrlKeyDown() {
      return this.$store.state.classroom.isCtrlKeyDown
    },
    zoom() {
      return this.$store.getters['classroom/zoomAsset']?.asset
    },
    assetText() {
      return this.file.asset.text
    },
  },
  watch: {
    text() {
      this.changeHandler(this.text)
    },
    isCtrlKeyDown(newValue, oldValue) {
      if (this.frameEl) {
        if (newValue) {
          this.frameEl.classList.add('no-scroll')
        } else {
          this.frameEl.classList.remove('no-scroll')
        }
      }
    },
    assetText(newValue, oldValue) {
      this.setText(newValue)
    },
  },
  beforeDestroy() {
    if (this.frameEditAreaEl) {
      ;[
        'pointerdown',
        'pointerup',
        'pointerout',
        'pointerleave',
      ].forEach((evt) =>
        this.frameEditAreaEl.removeEventListener(
          evt,
          this.pointerUpHandler,
          false
        )
      )

      this.frameEditAreaEl.removeEventListener(
        'pointerdown',
        this.pointerDownHandler,
        false
      )
      this.frameEditAreaEl.removeEventListener(
        'pointermove',
        this.pointerMoveHandler,
        false
      )
    }
  },
  methods: {
    async initHandler() {
      await this.setText(this.assetText)

      this.frameEl = document.getElementById(`${this.frameElId}_ifr`)
      this.frameDocument =
        this.frameEl.contentDocument || this.frameEl.contentWindow.document

      this.frameEl.setAttribute('data-hj-allow-iframe', true)

      this.frameEditAreaEl = this.frameDocument.getElementById('tinymce')

      if (this.frameEditAreaEl) {
        ;[
          'pointerdown',
          'pointerup',
          'pointerout',
          'pointerleave',
        ].forEach((evt) =>
          this.frameEditAreaEl.addEventListener(
            evt,
            this.pointerUpHandler,
            false
          )
        )

        this.frameEditAreaEl.addEventListener(
          'pointerdown',
          this.pointerDownHandler,
          false
        )
        this.frameEditAreaEl.addEventListener(
          'pointermove',
          this.pointerMoveHandler,
          false
        )
      }
    },
    setText(value) {
      return new Promise((resolve) => {
        this.text = value

        resolve()
      })
    },
    mouseOverHandler(e) {
      this.$emit('mouse-move', {
        ...e,
        clientX:
          (this.file.asset.left - this.zoom.x + e.clientX) *
          this.zoom.zoomIndex,
        clientY:
          (this.file.asset.top - this.zoom.y + e.clientY + 40) *
          this.zoom.zoomIndex,
      })
    },
    pointerDownHandler(e) {
      this.$emit('pointer-down', e)
    },
    pointerMoveHandler(e) {
      this.$emit('pointer-move', e)
    },
    pointerUpHandler(e) {
      this.$emit('pointer-up', e)
    },
    clickHandler() {
      if (!this.assetText) {
        this.setStartCursorPos()
      }
    },
    focusHandler(scope) {
      scope.onIndex()
    },
    dragOverHandler(e) {
      e.preventDefault()

      this.$store.commit('classroom/isDraggingTrigger', true)
    },
    keyDownHandler(e) {
      this.$store.commit('classroom/SET_IS_CTRL_KEY_DOWN', e.ctrlKey)

      this.pressButtonKey = e.keyCode
    },
    keyUpHandler(e) {
      this.$store.commit('classroom/SET_IS_CTRL_KEY_DOWN', false)

      if ([37, 38, 39, 40].includes(e.keyCode)) {
        this.setStartCursorPos()
      }
    },
    setStartCursorPos() {
      this.startCursorPos = this.getCaretCharacterOffsetWithin()
    },
    /**
     * Handle text updates and dispatch them to sockets
     */
    changeHandler(text) {
      if (!this.changedBySocket && !this.changedByDatabase) {
        this.endCursorPos = this.getCaretCharacterOffsetWithin()

        this.$socket.emit('text-editor-updated', {
          id: this.file.id,
          lessonId: this.file.lessonId,
          asset: {
            text,
            startPos: this.startCursorPos,
            endPos: this.endCursorPos,
            pressButtonKey: this.pressButtonKey,
            offsetTop: this.getCurrentNodeOffsetTop(),
          },
        })

        this.startCursorPos = this.endCursorPos

        this.updateAsset(text)
      } else {
        this.changedBySocket = false
        this.changedByDatabase = false
      }
    },
    updateAsset: debounce(function (text) {
      this.$store.dispatch('classroom/updateAssetWithoutSync', {
        id: this.file.id,
        lessonId: this.file.lessonId,
        asset: {
          text,
        },
      })
    }, 500),
    getCaretCharacterOffsetWithin() {
      const element = this.frameEditAreaEl?.childNodes?.[0]

      if (element) {
        const doc = element.ownerDocument || element.document
        const win = doc.defaultView || doc.parentWindow

        let sel
        let caretOffset = 0

        if (typeof win?.getSelection !== 'undefined') {
          sel = win.getSelection()

          if (sel.rangeCount > 0) {
            const range = sel.getRangeAt(0)
            const preCaretRange = range.cloneRange()

            preCaretRange.selectNodeContents(element)
            preCaretRange.setEnd(range.endContainer, range.endOffset)

            caretOffset = preCaretRange.toString().length
          }
        } else if ((sel = doc.selection) && sel.type !== 'Control') {
          const textRange = sel.createRange()
          const preCaretTextRange = doc.body.createTextRange()

          preCaretTextRange.moveToElementText(element)
          preCaretTextRange.setEndPoint('EndToEnd', textRange)

          caretOffset = preCaretTextRange.text.length
        }

        return caretOffset
      }

      return 0
    },
    getCurrentNodeOffsetTop() {
      const element = this.frameEditAreaEl?.childNodes?.[0]

      if (element) {
        const doc = element.ownerDocument || element.document
        const win = doc.defaultView || doc.parentWindow

        let sel
        let offsetTop = 0

        if (typeof win?.getSelection !== 'undefined') {
          sel = win.getSelection()

          offsetTop = sel?.anchorNode?.parentElement?.offsetTop ?? 0
        }

        return offsetTop
      }

      return 0
    },
    getCurrentNodeWithPosition(nodes) {
      return new Promise((resolve) => {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i]?.childNodes?.length > 0) {
            this.getCurrentNodeWithPosition(nodes[i]?.childNodes)
          } else if (!this.isCaretPositionFound) {
            this.previousNode = this.currentNode
            this.currentNode = nodes[i]

            if (this.previousNode?.textContent.length) {
              this.offset -= this.previousNode?.textContent.length
            }

            if (this.offset <= this.currentNode?.textContent.length) {
              this.isCaretPositionFound = true

              break
            }
          }
        }

        resolve()
      })
    },
    async setCaretPosition(nodes, position) {
      const sel = this.frameDocument.getSelection()
      const range = sel.getRangeAt(0)

      this.offset = position
      this.currentNode = null
      this.previousNode = null
      this.isCaretPositionFound = false

      await this.getCurrentNodeWithPosition(nodes)

      // move caret to specified offset
      if (this.currentNode != null && this.currentNode.length >= this.offset) {
        range.setStart(this.currentNode, this.offset)
        range.collapse(true)
        sel.removeAllRanges()
        sel.addRange(range)
      }
    },
    generatePdf() {
      this.$store.dispatch('lesson/generatePdf', this.file.lessonId)
    },
  },
  sockets: {
    async 'text-editor-updated'(data) {
      this.changedBySocket = true

      if (data.asset.text !== this.frameEditAreaEl?.innerHTML) {
        await this.setText(data.asset.text)

        // If edit node is not visible, scroll to that one
        if (
          data.asset.offsetTop > 0 &&
          this.frameEl?.contentWindow &&
          (this.frameEl.contentWindow.document.body.offsetHeight <
            data.asset.offsetTop ||
            this.frameEl.contentWindow.scrollY > data.asset.offsetTop)
        ) {
          this.frameEl.contentWindow.scrollTo({
            top: data.asset.offsetTop - 15,
            behavior: 'smooth',
          })
        }

        if (this.startCursorPos != null) {
          if (
            (data.asset.endPos < data.asset.startPos &&
              this.startCursorPos >= data.asset.startPos) ||
            (data.asset.endPos > data.asset.startPos &&
              this.startCursorPos > data.asset.startPos)
          ) {
            this.startCursorPos =
              this.startCursorPos + data.asset.endPos - data.asset.startPos
          }

          if (
            this.startCursorPos > 0 &&
            this.startCursorPos > data.asset.startPos &&
            data.asset.endPos === data.asset.startPos &&
            data.asset.pressButtonKey !== 13
          ) {
            this.startCursorPos = this.startCursorPos - 1
          }

          await this.setCaretPosition(
            this.frameEditAreaEl?.childNodes,
            this.startCursorPos
          )
        }
      }
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';

body {
  &.handle-tl,
  &.handle-tm,
  &.handle-tr,
  &.handle-mr,
  &.handle-br,
  &.handle-bm,
  &.handle-bl,
  &.handle-ml {
    .tox-sidebar-wrap::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  .tox-tinymce-aux {
    z-index: 100001 !important;
  }

  .tox {
    font-family: 'Lato', sans-serif !important;

    .tox-dialog-wrap__backdrop {
      background-color: rgba(0, 0, 0, 0.4);
    }

    .tox-dialog {
      box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1);
      border-radius: 20px;
      border: none;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        border-radius: 15px;
      }

      &__header {
        padding: 16px 30px 0 30px;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding: 12px 15px 0 15px;
        }
      }

      &__title {
        font-size: 20px;
        font-weight: 700;
        font-family: inherit;
        color: var(--v-success-base);
        background: linear-gradient(
          -75deg,
          var(--v-success-base),
          var(--v-primary-base)
        );
        background: -moz-linear-gradient(
          -75deg,
          var(--v-success-base),
          var(--v-primary-base)
        );
        background: -webkit-linear-gradient(
          -75deg,
          var(--v-success-base),
          var(--v-primary-base)
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      &__body-content {
        padding: 16px 30px;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding: 16px 15px;
        }
      }

      &__footer {
        padding: 8px 30px 24px;
        border-top: none;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding: 8px 15px 15px;
        }

        .tox-button {
          min-width: 162px;
          height: 38px;
          border-radius: 20px;
          font-weight: 600;
          font-size: 15px;

          @media #{map-get($display-breakpoints, 'xs-only')} {
            min-width: 120px;
          }
        }
      }
    }

    .tox-button {
      font-family: inherit;
      cursor: pointer !important;

      * {
        cursor: pointer !important;
      }

      &:not(.tox-button--secondary):not(.tox-button--naked) {
        background: linear-gradient(
          126.15deg,
          var(--v-success-base) 0%,
          var(--v-primary-base) 102.93%
        );
        border: none !important;

        &:hover {
          background: linear-gradient(
            305.26deg,
            var(--v-success-base) 8.94%,
            var(--v-primary-base) 110.83%
          ) !important;
        }
      }
    }

    label {
      font-family: inherit !important;
    }

    .tox-listboxfield .tox-listbox--select,
    .tox-textarea,
    .tox-textfield {
      font-family: inherit;
      cursor: auto !important;
    }
  }

  &.teacher-role {
    .tox {
      .tox-listboxfield .tox-listbox--select,
      .tox-textarea,
      .tox-textfield {
        &:focus {
          border-color: var(--v-teacherColor-base);
        }
      }
    }
  }

  &.student-role {
    .tox {
      .tox-listboxfield .tox-listbox--select,
      .tox-textarea,
      .tox-textfield {
        &:focus {
          border-color: var(--v-studentColor-base);
        }
      }
    }
  }
}

.no-scroll {
  pointer-events: none;
}

.classroom-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  box-shadow: 0 2px 30px rgb(0 0 0 / 25%);

  button,
  button *,
  div[role='button'],
  div[role='button'] * {
    cursor: auto !important;
  }

  .tox {
    &-sidebar-wrap {
      position: relative;
    }

    &-edit-area {
      * {
        cursor: auto !important;
      }

      a {
        cursor: pointer !important;
      }
    }

    &-statusbar__path {
      display: none !important;
    }
  }
}
</style>

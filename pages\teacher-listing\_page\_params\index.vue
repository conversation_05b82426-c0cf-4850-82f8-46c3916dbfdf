<template>
  <v-row>
    <teacher-listing
      :teachers="teachers"
      :params="params"
      :faq-items="faqItems"
      :page="page"
    ></teacher-listing>
  </v-row>
</template>

<script>
import TeacherListing from '~/components/teacher-listing/TeacherListing'

export default {
  name: 'TeacherListingFiltersPage',
  components: { TeacherListing },
  middleware: 'teacherListingRedirect',
  async asyncData({ params, store, query }) {
    const page = +params.page
    const currentCurrency = store.state.currency.item
    const searchQuery = query?.search
    const selectedSorting = store.state.teacher_filter.selectedSorting
    let paramsStr = params.params

    let filters

    if (!paramsStr.includes('sortOption')) {
      paramsStr += `;sortOption,${selectedSorting.id}`
    }

    await store
      .dispatch('teacher_filter/getFilters')
      .then((data) => (filters = data))
    await store.dispatch('teacher/getTeachers', {
      page,
      perPage: process.env.NUXT_ENV_PER_PAGE,
      params: paramsStr,
      searchQuery,
    })

    store.commit('teacher_filter/RESET_ACTIVE_FILTERS')
    store.commit('teacher_filter/SET_NEED_UPDATE_TEACHERS', false)

    const selectedTeacherPreference = filters.teacherPreference.find(
      (item) => item.id === 0
    )

    let specialities = []
    let selectedCurrency = filters.currencies.find(
      (item) => item.id === currentCurrency.id
    )

    if (selectedTeacherPreference) {
      store.commit('teacher_filter/SET_SELECTED_TEACHER_PREFERENCE', {
        teacherPreference: selectedTeacherPreference,
        updateActiveFilters: true,
      })
    }

    if (searchQuery) {
      store.commit('teacher_filter/SET_SEARCH_QUERY', {
        searchQuery,
        updateActiveFilters: true,
      })
    }

    paramsStr.split(';').forEach((item) => {
      let arr = item.split(',')

      const property = arr[0]

      arr.splice(0, 1)
      arr = arr.map((item) => +item)

      if (property === 'sortOption') {
        const sortByItem = store.getters[
          'teacher_filter/sortByItems'
        ]?.find((item) => arr.includes(item.id))

        if (sortByItem && !sortByItem.isFeedbackTag) {
          store.commit('teacher_filter/SET_SELECTED_SORTING', sortByItem)
        }
      }

      if (property === 'language') {
        const language = filters?.languages.find((item) =>
          arr.includes(item.id)
        )

        if (language) {
          store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
            language,
            updateActiveFilters: true,
          })
        }
      }

      if (property === 'motivation') {
        const motivation = filters?.motivations.find((item) =>
          arr.includes(item.id)
        )

        if (motivation) {
          specialities = motivation.specialities

          store.commit('teacher_filter/SET_SELECTED_MOTIVATION', {
            motivation,
            updateActiveFilters: true,
          })
          store.commit('teacher_filter/SET_SPECIALITIES', specialities)
        }
      }

      if (property === 'speciality') {
        const selectedSpecialities = specialities.filter((item) =>
          arr.includes(item.id)
        )

        store.commit('teacher_filter/SET_SELECTED_SPECIALITIES', {
          specialities: selectedSpecialities,
          updateActiveFilters: true,
        })
      }

      if (property === 'proficiencyLevels') {
        const proficiencyLevel = filters?.proficiencyLevels.find((item) =>
          arr.includes(item.id)
        )

        if (proficiencyLevel) {
          store.commit('teacher_filter/SET_SELECTED_PROFICIENCY_LEVEL', {
            proficiencyLevel,
            updateActiveFilters: true,
          })
        }
      }

      if (property === 'teacherPreference') {
        const teacherPreference = filters?.teacherPreference.find((item) =>
          arr.includes(item.id)
        )

        if (teacherPreference) {
          store.commit('teacher_filter/SET_SELECTED_TEACHER_PREFERENCE', {
            teacherPreference,
            updateActiveFilters: true,
          })
        }
      }

      if (property === 'matchLanguages') {
        const teacherPreferenceLanguage = filters?.languages.find((item) =>
          arr.includes(item.id)
        )

        if (teacherPreferenceLanguage) {
          store.commit(
            'teacher_filter/SET_SELECTED_TEACHER_PREFERENCE_LANGUAGE',
            { teacherPreferenceLanguage, updateActiveFilters: true }
          )
        }
      }

      if (property === 'dates') {
        const dates = store.state.teacher_filter.days.filter((item) =>
          arr.includes(item.id)
        )

        store.commit('teacher_filter/SET_SELECTED_DAYS', {
          dates,
          updateActiveFilters: true,
        })
      }

      if (property === 'time') {
        const times = store.state.teacher_filter.times.filter((item) =>
          arr.includes(item.id)
        )

        store.commit('teacher_filter/SET_SELECTED_TIMES', {
          times,
          updateActiveFilters: true,
        })
      }

      if (property === 'currency') {
        selectedCurrency = filters?.currencies.find((item) =>
          arr.includes(item.id)
        )
      }

      if (property === 'tag') {
        const feedbackTag = store.getters[
          'teacher_filter/feedbackTags'
        ].find((item) => arr.includes(item.id))

        if (feedbackTag) {
          store.commit('teacher_filter/SET_SELECTED_FEEDBACK_TAG', feedbackTag)
        }
      }
    })

    if (selectedCurrency) {
      await store.dispatch('currency/setItem', { item: selectedCurrency })
      await store.dispatch('teacher_filter/updateCurrencyActiveFilter')
    }

    return { page, params: paramsStr }
  },
  head() {
    return {
      title: this.$t('teacher_listing_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('teacher_listing_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} teacher-listing-page`,
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    teachers() {
      return this.$store.state.teacher.items
    },
    faqItems() {
      return this.$store.state.faq.teacherListItems
    },
  },
  watchQuery: true,
  async beforeMount() {
    if (!this.faqItems.length) {
      await this.$store.dispatch('loadingAllow', false)
      this.$store
        .dispatch('faq/getTeacherListPageFaqs')
        .finally(() => this.$store.dispatch('loadingAllow', true))
    }
  },
}
</script>

<style lang="scss">
@import './assets/styles/teacher-listing.scss';
</style>

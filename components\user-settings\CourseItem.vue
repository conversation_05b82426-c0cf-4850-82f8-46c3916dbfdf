<template>
  <v-expansion-panel class="course-panel">
    <v-expansion-panel-header disable-icon-rotate>
      {{ title }}
      <template #actions>
        <template v-if="isActive">
          <v-icon color="darkLight">
            {{ mdiMinus }}
          </v-icon>
        </template>
        <template v-else>
          <v-img
            :src="require('~/assets/images/add-icon-gradient.svg')"
            width="24"
            height="24"
          ></v-img>
        </template>
      </template>
    </v-expansion-panel-header>
    <v-expansion-panel-content eager>
      <v-form
        :ref="`form-${index}`"
        :value="valid"
        @input="formValid = $event"
        @submit.prevent="saveCourse"
      >
        <div class="mb-md-2">
          <v-row>
            <v-col class="col-12 col-sm-6 d-flex align-end">
              <div class="input-wrap">
                <div class="input-wrap-label">
                  {{ $t('please_enter_name_of_course') }}
                </div>
                <text-input
                  :value="item.name"
                  type-class="border-gradient"
                  height="44"
                  counter="50"
                  :hide-details="false"
                  :rules="rules.name"
                  :placeholder="$t('name_of_course')"
                  @input="updateValue($event, 'name')"
                ></text-input>
              </div>
            </v-col>
            <v-col class="col-12 col-sm-6 d-flex align-end">
              <div class="input-wrap">
                <div class="input-wrap-label">
                  {{ $t('number_of_lessons_in_course') }}
                </div>
                <user-setting-select
                  :value="{ name: item.lessons }"
                  :items="lessonCountItems"
                  :hide-details="false"
                  item-value="name"
                  :attach-id="`course-lessons-${item.id || item.uid}`"
                  @change="updateValue($event.name, 'lessons')"
                ></user-setting-select>
              </div>
            </v-col>
          </v-row>
        </div>
        <div class="mb-sm-2">
          <v-row>
            <v-col class="col-12 col-sm-6 d-flex mb-2 mb-sm-0">
              <div class="input-wrap">
                <div class="input-wrap-label">
                  {{ $t('duration_of_lessons') }}
                </div>
                <user-setting-select
                  :value="{ name: item.length }"
                  :items="lessonLengthItems"
                  item-value="name"
                  :attach-id="`course-length-${item.id || item.uid}`"
                  @change="
                    ($event) => {
                      updateValue($event.name, 'length')
                      currentSelectedDuration = $event.name
                    }
                  "
                ></user-setting-select>
              </div>
            </v-col>
            <v-col class="col-12 col-sm-6 d-flex mb-2 mb-sm-0">
              <div class="input-wrap">
                <div class="input-wrap-label">
                  {{ $t('price_per_lesson') }}
                </div>
                <lesson-price
                  :value="item.price"
                  @validation="priceValid = $event"
                  @input="updatePriceTrialLesson"
                  :length="currentSelectedDuration"
                ></lesson-price>
                <div
                  v-if="totalPackagePrice"
                  class="input-wrap-label mt-1 mb-0"
                >
                  {{ $t('Total package price') }}: {{ currentCurrencySymbol
                  }}{{ totalPackagePrice.toFixed(2) }}
                </div>
              </div>
            </v-col>
          </v-row>
        </div>
        <div class="mb-2 mb-md-4">
          <v-row>
            <v-col class="col-12 col-sm-6 d-flex">
              <div class="input-wrap">
                <div class="input-wrap-label">
                  {{ $t('language_of_course') }}
                </div>
                <user-setting-select
                  :value="{ id: item.languageId }"
                  :items="languages"
                  :hide-selected="false"
                  :attach-id="`course-language-${item.id || item.uid}`"
                  @change="updateValue($event.id, 'languageId')"
                ></user-setting-select>
              </div>
            </v-col>
          </v-row>
        </div>
        <div class="mb-md-2">
          <v-row>
            <v-col class="col-12 col-sm-6 d-flex">
              <div class="input-wrap">
                <div class="input-wrap-label">
                  {{ $t('please_enter_short_description_of_course') }}:
                </div>
                <div>
                  <v-textarea
                    :value="item.shortDescription"
                    class="l-textarea"
                    no-resize
                    height="234"
                    solo
                    dense
                    counter="250"
                    :rules="rules.shortDescription"
                    @input="updateValue($event, 'shortDescription')"
                  ></v-textarea>
                </div>
              </div>
            </v-col>
            <v-col class="col-12 col-sm-6 d-flex">
              <div class="input-wrap">
                <div class="input-wrap-label">
                  {{ $t('please_enter_intro_to_course') }}:
                </div>
                <div>
                  <v-textarea
                    :value="item.introductionToCourse"
                    class="l-textarea"
                    no-resize
                    height="234"
                    solo
                    dense
                    counter="500"
                    :rules="rules.description"
                    @input="updateValue($event, 'introductionToCourse')"
                  ></v-textarea>
                </div>
              </div>
            </v-col>
          </v-row>
        </div>
        <div class="mb-2 mb-md-3">
          <v-row>
            <v-col class="col-12 d-flex">
              <div class="input-wrap">
                <div class="input-wrap-label">
                  {{ $t('please_enter_structure_of_course') }}:
                </div>
                <div>
                  <editor
                    :value="item.courseStructure"
                    :limit="1500"
                    counter
                    @validation="courseStructureValid = $event"
                    @update="updateCourseStructure"
                  ></editor>
                </div>
              </div>
            </v-col>
          </v-row>
        </div>
        <div>
          <v-row>
            <v-col class="col-12 col-sm-6 d-flex mb-2 mb-sm-0">
              <div class="input-wrap input-wrap--image">
                <div class="input-wrap-label">
                  {{
                    $t('please_choose_illustration_that_best_suits_to_course')
                  }}:
                </div>
                <div class="image-select">
                  <template v-if="!item.image">
                    <div class="image-select--no-data body-2">
                      {{ $t('no_illustration_chosen') }}
                    </div>
                  </template>
                  <template v-else>
                    <div class="d-flex align-center">
                      <div class="image-select-preview">
                        <v-img
                          eager
                          :src="
                            require(`~/assets/images/course-illustrations/${item.image}.svg`)
                          "
                        ></v-img>
                      </div>
                      <div class="image-select-name">
                        {{ currentIllustration.name }}
                        <v-btn
                          width="18"
                          height="18"
                          icon
                          @click="isShownIllustrationConfirmDialog = true"
                        >
                          <v-img
                            :src="require('~/assets/images/close-gradient.svg')"
                            width="15"
                            height="15"
                          ></v-img>
                        </v-btn>
                      </div>
                    </div>
                  </template>
                </div>
                <v-btn
                  class="gradient font-weight-medium mt-3"
                  @click="isShownIllustrationDialog = true"
                >
                  <div class="text--gradient">
                    {{ $t('choose_illustration') }}
                  </div>
                </v-btn>
              </div>
            </v-col>
            <v-col class="col-12 col-sm-6">
              <div class="input-wrap">
                <div class="input-wrap-label">
                  {{ $t('course_intro_video_please_enter_youtube_link') }}
                </div>
                <text-input
                  :value="item.youtube"
                  type-class="border-gradient"
                  height="44"
                  hide-details
                  :placeholder="$t('youtube_link')"
                  @input="updateValue($event, 'youtube')"
                ></text-input>
              </div>
              <div class="mt-3">
                <div class="input-wrap-label mb-0">
                  {{ $t('are_you_ready_to_publish_this_course') }} <br />
                  {{
                    $t(
                      'select_this_option_when_you_are_ready_to_begin_selling_this_course'
                    )
                  }}
                </div>
                <div class="d-flex justify-end">
                  <v-switch
                    v-model="isPublish"
                    inset
                    :ripple="false"
                    color="success"
                    dense
                    hide-details
                  ></v-switch>
                </div>
              </div>
            </v-col>
          </v-row>
        </div>
        <div>
          <v-row>
            <v-col class="col-12">
              <div class="d-flex justify-space-between justify-sm-end mt-3">
                <v-btn
                  color="error"
                  class="font-weight-medium mt-1 mx-1"
                  @click="isShownCourseConfirmDialog = true"
                >
                  {{ $t('delete_course') }}
                </v-btn>
                <v-btn
                  color="primary"
                  class="font-weight-medium mt-1 mx-1"
                  :disabled="!valid"
                  type="submit"
                >
                  <svg class="mr-1" width="18" height="18" viewBox="0 0 18 18">
                    <use
                      :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#save-icon`"
                    ></use>
                  </svg>
                  {{ $t('save_changes') }}
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </div>
      </v-form>

      <illustration-dialog
        :key="key"
        :is-shown-illustration-dialog="isShownIllustrationDialog"
        :current-illustration="currentIllustration"
        @update-image="updateImage"
        @close-dialog="isShownIllustrationDialog = false"
      ></illustration-dialog>

      <confirm-dialog
        :is-shown-confirm-dialog="isShownIllustrationConfirmDialog"
        @confirm="removeImage"
        @close-dialog="isShownIllustrationConfirmDialog = false"
      >
        {{
          $t(
            'illustration_will_be_deleted_from_course_you_will_have_to_choose_another_one'
          )
        }}
        <br />
        {{ $t('do_you_confirm_that') }}
      </confirm-dialog>

      <confirm-dialog
        :is-shown-confirm-dialog="isShownCourseConfirmDialog"
        cancel-text-button="no"
        confirm-text-button="yes"
        @confirm="removeCourse"
        @close-dialog="isShownCourseConfirmDialog = false"
      >
        {{ $t('are_you_sure_you_want_to_delete_this_course_permanently') }}
      </confirm-dialog>
    </v-expansion-panel-content>
  </v-expansion-panel>
</template>

<script>
import TextInput from '@/components/form/TextInput'
import LessonPrice from '@/components/user-settings/LessonPrice'
import UserSettingSelect from '@/components/user-settings/UserSettingSelect'
import Editor from '@/components/form/Editor'
import IllustrationDialog from '@/components/user-settings/IllustrationDialog'
import ConfirmDialog from '@/components/ConfirmDialog'
import { mdiMinus } from '@mdi/js'

export default {
  name: 'CourseItem',
  components: {
    TextInput,
    LessonPrice,
    UserSettingSelect,
    Editor,
    IllustrationDialog,
    ConfirmDialog,
  },
  props: {
    item: {
      type: Object,
      required: true,
    },
    languages: {
      type: Array,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
    isActive: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      mdiMinus,
      title: '',
      key: 1,
      formValid: true,
      priceValid: true,
      courseStructureValid: true,
      rules: {
        name: [
          (v) => v && v.length <= 50,
          () => this.titleIsUnique || this.$t('name_should_be_unique'),
        ],
        shortDescription: [(v) => v && v.length <= 250],
        description: [(v) => v && v.length <= 500],
      },
      isShownIllustrationDialog: false,
      isShownIllustrationConfirmDialog: false,
      isShownCourseConfirmDialog: false,
      currentSelectedDuration: 30,
    }
  },
  computed: {
    lessonCountItems() {
      return this.$store.state.settings.lessonCountItems
    },
    lessonLengthItems() {
      return this.$store.state.settings.lessonLengthItems
    },
    totalPackagePrice() {
      return this.item.price * this.item.lessons
    },
    illustrationItems() {
      return this.$store.state.settings.illustrationItems
    },
    currentIllustration() {
      if (!this.item.image) return {}

      const [item] = this.illustrationItems.filter(
        (el) => el.image === this.item.image
      )

      return item
    },
    isPublish: {
      get() {
        return this.item.isPublish
      },
      set(value) {
        this.updateValue(value, 'isPublish')
      },
    },
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    courses() {
      return this.$store.state.settings.courseItems
    },
    titleIsUnique() {
      return !this.courses
        .filter(
          (item) =>
            (item.id && item.id !== this.item.id) ||
            (item.uid && item.uid !== this.item.uid)
        )
        .map((item) => item.name)
        .includes(this.item.name)
    },
    valid() {
      return (
        this.titleIsUnique &&
        this.formValid &&
        this.priceValid &&
        !!this.currentIllustration?.id &&
        this.courseStructureValid
      )
    },
  },
  watch: {
    titleIsUnique(newValue, oldValue) {
      this.$refs[`form-${this.index}`]?.validate()
    },
  },
  mounted() {
    this.setTitle()
  },
  methods: {
    setTitle() {
      this.title = this.item.name || `${this.$t('course')} ${this.index + 1}`
    },
    updateImage(value) {
      this.isShownIllustrationDialog = false

      this.updateValue(value, 'image')
    },
    updatePriceTrialLesson(value) {
      this.updateValue(value ? Number.parseFloat(value) : 0.0, 'price')
    },
    removeImage() {
      this.isShownIllustrationConfirmDialog = false

      this.updateValue(null, 'image')

      this.key++
    },
    updateCourseStructure(value) {
      this.updateValue(value, 'courseStructure')
    },
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_COURSE_ITEM', {
        ...this.item,
        [property]: value,
      })
    },
    async removeCourse() {
      if (this.item.id) {
        await this.$store.dispatch('settings/removeCourse', this.item.id)
      }

      this.$store.commit('settings/REMOVE_COURSE_ITEM', this.item)

      this.isShownCourseConfirmDialog = false

      this.$emit('scroll-to-top')
    },
    saveCourse() {
      const item = { ...this.item }

      delete item.slug

      this.$store
        .dispatch(
          `settings/${this.item.uid ? 'addCourse' : 'updateCourse'}`,
          item
        )
        .then(() => this.setTitle())
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';

.course-panel {
  .v-expansion-panel {
    &-header {
      min-height: 75px;
      padding: 24px 0;
      font-size: 20px;
      font-weight: 600;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        padding: 20px 0;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        min-height: 48px;
        padding: 10px 0;
        font-size: 16px;
      }
    }

    &-content__wrap {
      padding: 16px 0 40px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        padding: 16px 0 32px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        padding: 16px 0 24px;
      }
    }
  }

  .input-wrap--image {
    .image-select {
      color: var(--v-grey-base);

      &-preview {
        width: 80px;
        height: 80px;
        border-radius: 16px;
        background: linear-gradient(95.18deg, #f2f8e9 15.34%, #ebf3fe 66.75%),
          #c4c4c4;
        overflow: hidden;
      }

      &-name {
        position: relative;
        margin-left: 12px;
        padding-right: 32px;
        font-size: 14px;

        .v-btn {
          position: absolute;
          right: 0;
          top: 1px;
        }
      }
    }
  }
}
</style>

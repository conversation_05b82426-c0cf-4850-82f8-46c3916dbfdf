<template>
  <v-col v-resize="setViewportWidth" class="col-12 pa-0">
    <div class="teacher-profile">
      <v-container fluid class="py-0">
        <v-row>
          <v-col class="col-12 px-0">
            <div class="teacher-profile-wrap">
              <div class="teacher-profile-content">
                <steps class="d-none d-sm-block" :active-item-id="2"></steps>

                <div class="general teacher-profile-panel">
                  <div class="teacher-profile-card">
                    <div class="teacher-profile-card-top">
                      <l-avatar
                        class="teacher-profile-card-avatar"
                        :avatars="userProfile.avatars"
                        :languages-taught="userProfile.languagesTaught"
                        clicked
                        @show-full-avatar="shownAvatarDialog"
                      ></l-avatar>

                      <div class="teacher-profile-card-top-helper">
                        <div
                          :class="[
                            'teacher-profile-card-general-info',
                            {
                              'new-teacher': userProfile.countFeedbacks === 0,
                            },
                            { 'has-feedback-tags': hasfeedbackTags },
                          ]"
                        >
                          <div>
                            <h1
                              class="teacher-profile-card-name font-weight-medium"
                            >
                              {{ fullName }}
                            </h1>
                            <div
                              class="teacher-profile-card-short-description d-none d-md-block"
                            >
                              {{ userProfile.shortSummary }}
                            </div>
                          </div>
                          <div class="teacher-profile-card-rating">
                            <template v-if="userProfile.countFeedbacks === 0">
                              <div class="new-verified-teacher">
                                <svg
                                  width="18"
                                  height="18"
                                  viewBox="0 0 612 612"
                                >
                                  <use
                                    :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#verified-user`"
                                  ></use>
                                </svg>
                                <div class="body-2 pl-1">
                                  {{ $t('new_verified_teacher') }}
                                </div>
                              </div>
                            </template>
                            <template v-else>
                              <star-rating
                                class="mr-1 mr-sm-2 mr-md-0"
                                :value="userProfile.averageRatings"
                                large
                              ></star-rating>
                              <div class="review mt-md-1">
                                ({{
                                  $tc('review', userProfile.countFeedbacks)
                                }})
                              </div>
                            </template>
                          </div>
                        </div>
                        <feedback-tags
                          :items="feedbackTags"
                          class="d-none d-md-block"
                        ></feedback-tags>
                        <div
                          class="teacher-profile-card-features d-none d-md-flex"
                        >
                          <div
                            v-if="userProfile.languagesTaught.length"
                            class="item"
                          >
                            <div class="item__title">{{ $t('teaches') }}:</div>
                            <div class="item__text">
                              {{ userProfile.languagesTaught | languagesToStr }}
                            </div>
                          </div>
                          <div
                            v-if="userProfile.nativeLanguages.length"
                            class="item"
                          >
                            <div class="item__title">
                              {{ $t('native_languages') }}:
                            </div>
                            <div class="item__text">
                              {{ userProfile.nativeLanguages | languagesToStr }}
                            </div>
                          </div>
                          <div
                            v-if="userProfile.otherLanguagesSpoken.length"
                            class="item"
                          >
                            <div class="item__title">
                              {{ $t('other_languages_spoken') }}:
                            </div>
                            <div class="item__text">
                              {{
                                userProfile.otherLanguagesSpoken
                                  | languagesToStr
                              }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      :class="[
                        'teacher-profile-card-short-description d-md-none',
                        { 'has-feedback-tags': hasfeedbackTags },
                      ]"
                    >
                      {{ userProfile.shortSummary }}
                    </div>
                    <feedback-tags
                      v-if="$vuetify.breakpoint.smAndDown"
                      :items="feedbackTags"
                      class="d-md-none"
                    ></feedback-tags>
                    <div class="teacher-profile-card-features mb-3 d-md-none">
                      <div class="d-flex">
                        <div
                          v-if="userProfile.languagesTaught.length"
                          class="item mb-3 mr-2"
                        >
                          <div class="item__title">{{ $t('teaches') }}:</div>
                          <div class="item__text">
                            {{ userProfile.languagesTaught | languagesToStr }}
                          </div>
                        </div>
                        <div
                          v-if="userProfile.nativeLanguages.length"
                          class="item mb-3"
                        >
                          <div class="item__title">
                            {{ $t('native_languages') }}:
                          </div>
                          <div class="item__text">
                            {{ userProfile.nativeLanguages | languagesToStr }}
                          </div>
                        </div>
                      </div>

                      <div
                        v-if="userProfile.otherLanguagesSpoken.length"
                        class="item"
                      >
                        <div class="item__title">
                          {{ $t('other_languages_spoken') }}:
                        </div>
                        <div class="item__text">
                          {{
                            userProfile.otherLanguagesSpoken | languagesToStr
                          }}
                        </div>
                      </div>
                    </div>
                    <div class="d-flex flex-column-reverse flex-sm-column">
                      <div
                        v-if="
                          userProfile.specialities.length ||
                          userProfile.qualifications.length
                        "
                        class="teacher-profile-card-specialities"
                      >
                        <div
                          v-if="userProfile.specialities.length"
                          :class="[
                            'specialities',
                            {
                              'specialities--full-width': !userProfile
                                .qualifications.length,
                            },
                          ]"
                        >
                          <div class="specialities-title">
                            {{ $t('specialities') }}:
                          </div>
                          <div class="specialities-content">
                            <template
                              v-for="speciality in userProfile.specialities"
                            >
                              <div
                                v-if="speciality.speciality.isPublish"
                                :key="speciality.id"
                                class="item"
                              >
                                <div class="item-icon">
                                  <component
                                    :is="
                                      iconComponents[speciality.speciality.icon]
                                    "
                                  ></component>
                                </div>
                                {{ speciality.speciality.name }}
                              </div>
                            </template>
                          </div>
                        </div>
                        <div
                          v-if="userProfile.qualifications.length"
                          class="qualifications"
                        >
                          <div class="qualifications-title">
                            {{ $t('qualifications') }}:
                          </div>
                          <div class="qualifications-content">
                            <div
                              v-for="qualification in qualifications"
                              :key="qualification.id"
                              class="item"
                            >
                              <div class="item-icon">
                                <CheckedGradientIcon />
                              </div>
                              {{ qualification.name }}
                            </div>
                            <div
                              v-if="
                                userProfile.qualifications.length > 3 &&
                                !showAllQualifications
                              "
                              class="more text--gradient"
                              @click.prevent.stop="showAllQualifications = true"
                            >
                              {{ $t('see_more') }}
                              <div>
                                <v-img
                                  :src="
                                    require('~/assets/images/chevron-gradient.svg')
                                  "
                                  width="12"
                                  height="12"
                                ></v-img>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        v-if="userProfile.videoLink"
                        class="mt-sm-4 mb-3 mb-sm-0"
                      >
                        <youtube :video-link="userProfile.videoLink"></youtube>
                      </div>
                    </div>
                    <div class="teacher-profile-card-description mt-3 mt-md-4">
                      <div>
                        <template v-if="userProfile.description">
                          <h6 class="text--gradient mb-1">{{ $t('bio') }}:</h6>
                          <p class="mb-3">{{ userProfile.description }}</p>
                        </template>

                        <template v-if="whatToExpectHtml">
                          <h6 class="text--gradient mb-1">
                            {{ $t('my_teaching_methodology') }}
                          </h6>
                          <div class="mb-3" v-html="whatToExpectHtml"></div>
                        </template>

                        <template v-if="userProfile.bio">
                          <h6 class="text--gradient mb-1">
                            {{ $t('my_teaching_background') }}:
                          </h6>
                          <p class="mb-3">{{ userProfile.bio }}</p>
                        </template>

                        <template v-if="userProfile.backgroundDescription">
                          <h6 class="text--gradient mb-1">
                            {{ $t('my_background_outside_of_teaching') }}:
                          </h6>
                          <p class="mb-3">
                            {{ userProfile.backgroundDescription }}
                          </p>
                        </template>
                      </div>
                      <aside v-if="factsAboutTeacher.length">
                        <div>
                          <div>
                            <h5 class="text--gradient">
                              {{ $t('unique_3_facts_about_me') }}:
                            </h5>
                            <ul>
                              <li
                                v-for="(fact, idx) in factsAboutTeacher"
                                :key="idx"
                              >
                                {{ fact }}
                              </li>
                            </ul>
                          </div>
                        </div>
                      </aside>
                    </div>
                  </div>
                </div>

                <teacher-profile-sidebar
                  v-if="$vuetify.breakpoint.xsOnly"
                  class="d-sm-none"
                  :slug="slug"
                  :current-time="currentTime"
                  :is-shown-time-picker-dialog="isShownTimePickerDialog"
                  @show-message-dialog="showMessageDialog"
                  @show-time-picker-dialog="showTimePickerDialog"
                  @update-current-time="currentTime = $event"
                ></teacher-profile-sidebar>

                <!--                <client-only>-->
                <div
                  v-if="courses.length"
                  class="courses teacher-profile-panel"
                >
                  <div class="courses-title">
                    {{
                      $tc('username_courses_count', courses.length, {
                        name: firstName,
                      })
                    }}
                    <span>({{ courses.length }})</span>
                  </div>
                  <div
                    v-if="hasFreeSlots && acceptNewStudents"
                    ref="coursesText"
                    class="courses-text"
                    v-html="
                      $t('teacher_offers_these_custom_designed_courses', {
                        username: firstName,
                      })
                    "
                  ></div>
                  <div class="courses-list">
                    <course-item
                      v-for="(course, idx) in coursesToShow"
                      :id="course.slug"
                      :key="course.id"
                      :item="course"
                      :index="idx + 1"
                      :username="slug"
                      :open-courses="openCourses"
                      @go-to-course="goToCourse($event)"
                      @toggle-show-course="toggleShowCourse"
                      @show-time-picker-dialog="showTimePickerDialog(true)"
                    ></course-item>
                  </div>
                  <div
                    v-if="isShownMoreButton"
                    class="courses-show-more text--gradient"
                    @click="showAllCourses(quantityCoursesToShowByDefault)"
                  >
                    {{ $t('see_all_courses') }}
                    <v-img
                      :src="require('~/assets/images/chevron-gradient.svg')"
                      width="12"
                      height="12"
                    ></v-img>
                  </div>
                </div>
                <div v-if="reviews.length" class="reviews">
                  <div class="reviews-title">
                    {{
                      $t('reviews_for_teacher_from_other_students', {
                        name: firstName,
                      })
                    }}&nbsp;&nbsp;
                    <div class="d-inline-block">
                      <star-rating
                        v-if="userProfile.countFeedbacks > 0"
                        :value="userProfile.averageRatings"
                        large
                      ></star-rating>
                    </div>
                    <span class="body-1 grey--text">
                      ({{ $tc('ratings_count', userProfile.countFeedbacks) }},
                      {{
                        $tc(
                          'written_reviews_count',
                          userProfile.countWrittenFeedbacks
                        )
                      }})
                    </span>
                  </div>
                  <div class="reviews-content">
                    <template v-if="$vuetify.breakpoint.mdAndUp">
                      <div class="reviews-list">
                        <div
                          v-for="i in Math.ceil(reviews.length / 2)"
                          :key="i"
                          :class="[
                            `reviews-row reviews-row--${i % 2 ? 't1' : 't2'}`,
                          ]"
                        >
                          <div
                            v-for="(review, idx) in reviews.slice(
                              (i - 1) * 2,
                              (i - 1) * 2 + 2
                            )"
                            :key="review.id"
                            :class="[
                              'item',
                              {
                                'item--gradient':
                                  (i % 2 && idx % 2) ||
                                  (!(i % 2) && !(idx % 2)),
                              },
                            ]"
                          >
                            <div class="item-helper">
                              <div class="item-text">
                                “{{ review.description }}”
                              </div>
                              <div class="item-bottom">
                                <div class="item-bottom-helper">
                                  <v-img
                                    :src="
                                      require('~/assets/images/homepage/user-icon-4.svg')
                                    "
                                    width="30"
                                    :options="{ rootMargin: '50%' }"
                                  ></v-img>
                                  {{ review.studentFirstName }},
                                  {{
                                    $tc(
                                      'review_lessons_count',
                                      review.countFinishedLesson,
                                      { language: review.language.name }
                                    )
                                  }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-else>
                      <client-only>
                        <VueSlickCarousel v-bind="reviewsCarouselSettings">
                          <div
                            v-for="(review, idx) in reviews.slice(0, 30)"
                            :key="review.id"
                            class="reviews-carousel-item"
                          >
                            <div
                              :class="['item', { 'item--gradient': idx % 2 }]"
                            >
                              <div class="item-helper">
                                <div class="item-text">
                                  “{{ review.description }}”
                                </div>
                                <div class="item-bottom">
                                  <div class="item-bottom-helper">
                                    <v-img
                                      :src="
                                        require('~/assets/images/homepage/user-icon-4.svg')
                                      "
                                      width="30"
                                      :options="{ rootMargin: '50%' }"
                                    ></v-img>
                                    {{ review.studentFirstName }},
                                    {{
                                      $tc(
                                        'review_lessons_count',
                                        review.countFinishedLesson,
                                        { language: review.language.name }
                                      )
                                    }}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </VueSlickCarousel>
                      </client-only>
                    </template>
                  </div>
                </div>
                <!--                </client-only>-->
              </div>
              <aside
                v-if="
                  viewportWidth === 0 ||
                  viewportWidth > $vuetify.breakpoint.thresholds.xs
                "
                class="teacher-profile-sidebar d-none d-sm-block"
              >
                <div
                  class="teacher-profile-sidebar-sticky"
                  :class="{ makeSticky: isSticky }"
                >
                  <div class="teacher-profile-sidebar-helper">
                    <teacher-profile-sidebar
                      :slug="slug"
                      :current-time="currentTime"
                      :is-shown-time-picker-dialog="isShownTimePickerDialog"
                      @show-message-dialog="showMessageDialog"
                      @show-time-picker-dialog="showTimePickerDialog"
                      @update-current-time="currentTime = $event"
                    ></teacher-profile-sidebar>
                  </div>
                </div>
              </aside>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </div>

    <div v-if="$vuetify.breakpoint.xsOnly" class="fixed-buttons d-sm-none">
      <div class="d-flex justify-center">
        <v-btn
          class="gradient gradient--bg-white font-weight-bold"
          width="165"
          @click="goTo('teacher-profile-prices')"
        >
          <div class="text--gradient">
            {{ $t('see_pricing') }}
          </div>
        </v-btn>
        <v-btn
          color="primary"
          class="font-weight-bold"
          width="165"
          @click="goTo('teacher-profile-free-slots')"
        >
          {{ $t('jump_to_calendar') }}
        </v-btn>
      </div>
    </div>

    <time-picker-dialog
      :is-shown-time-picker-dialog="isShownTimePickerDialog"
      :username="slug"
      :courses="courses"
      :languages="userProfile.languagesTaught"
      :query="query"
      :current-time="currentTime"
      @update-current-time="currentTime = $event"
      @next-step="goToSummaryLessonDialog"
      @close-dialog="closeTimePickerDialog"
    ></time-picker-dialog>

    <summary-lesson-dialog
      :is-shown-summary-lesson-dialog="isShownSummaryLessonDialog"
      :username="slug"
      :query="query"
      @prev-step="goToTimePicker"
      @close-dialog="closeSummaryDialog"
    ></summary-lesson-dialog>

    <message-dialog
      :recipient-id="userProfile.id"
      :recipient-name="fullName"
      :is-shown-message-dialog="isShownMessageDialog"
      @close-dialog="isShownMessageDialog = false"
    ></message-dialog>

    <l-dialog
      :dialog="isShownAvatarDialog"
      width="auto"
      max-width="unset"
      eager
      custom-class="avatar-dialog"
      @close-dialog="isShownAvatarDialog = false"
    >
      <img
        ref="usersMainImage"
        :src="userProfile.mainAvatar"
        :alt="firstName"
        @load="avatarLoaded"
      />
    </l-dialog>
  </v-col>
</template>

<script>
import VueSlickCarousel from 'vue-slick-carousel'

// Import the navigation state helper
import { saveNavigationState } from '@/helpers/navigationState'
import { getSlugByString, setStyleVariable } from '~/helpers'

import Steps from '~/components/Steps'
import Youtube from '~/components/Youtube'
import StarRating from '~/components/StarRating'
import TeacherProfileSidebar from '~/components/teacher-profile/TeacherProfileSidebar'
import FeedbackTags from '~/components/teacher-profile/FeedbackTags'
import CourseItem from '~/components/teacher-profile/CourseItem'
import LAvatar from '~/components/LAvatar'
import TimePickerDialog from '~/components/teacher-profile/TimePickerDialog'
import MessageDialog from '~/components/MessageDialog'
import SummaryLessonDialog from '~/components/SummaryLessonDialog'
import LDialog from '~/components/LDialog'
import CheckedGradientIcon from '~/components/images/CheckedGradientIcon'
import CareerGradientIcon from '~/components/images/CareerGradientIcon'
import EducationGradientIcon from '~/components/images/EducationGradientIcon'
import LifeGradientIcon from '~/components/images/LifeGradientIcon'

import 'vue-slick-carousel/dist/vue-slick-carousel.css'

export default {
  name: 'TeacherProfile',
  components: {
    Steps,
    Youtube,
    StarRating,
    FeedbackTags,
    TeacherProfileSidebar,
    CourseItem,
    LAvatar,
    TimePickerDialog,
    SummaryLessonDialog,
    MessageDialog,
    LDialog,
    CheckedGradientIcon,
    VueSlickCarousel,
  },
  filters: {
    languagesToStr(arr) {
      return arr.map((item) => item.name).join(', ')
    },
  },
  async asyncData({ store, $dayjs, params, query: _query }) {
    const slug = params.slug
    const query = { ..._query }
    const queryCurrencyIsoCode = query.currency

    let currencyIsoCode = store.getters['user/isUserLogged']
      ? store.getters['user/currency'].isoCode
      : store.state.currency.item.isoCode

    if (queryCurrencyIsoCode) {
      const currency = store.state.currency.items.find(
        (item) => item.isoCode === queryCurrencyIsoCode
      )

      if (currency) {
        currencyIsoCode = queryCurrencyIsoCode

        await store.dispatch('currency/setItem', {
          item: currency,
          isCookieUpdate: false,
        })
      }
    }

    await Promise.all([
      store.dispatch('teacher_profile/getItem', slug),
      store.dispatch('teacher_profile/getServices', {
        slug,
        currencyIsoCode,
      }),
      store.dispatch('teacher_profile/getSlots', {
        slug,
        date: $dayjs().day(1),
      }),
      store.dispatch('teacher_profile/getReviews', slug),
    ])

    const userProfile = store.state.teacher_profile.item

    const contentArray = (userProfile.whatToExpect || '').split(/\n/)

    let whatToExpectHtml = null
    let output = ''
    let isListStarted = false

    for (let i = 0; i < contentArray.length; i++) {
      const contentLine = contentArray[i]

      if (!contentLine.trim().length) {
        if (isListStarted) {
          isListStarted = false
          output += '</ul>'
        }
        continue
      }

      if (contentLine.substr(0, 1) !== '*') {
        if (isListStarted) {
          isListStarted = false
          output += '</ul>'
        }

        output += contentLine + ' '
        continue
      }

      if (!isListStarted && contentLine.substr(0, 1) === '*') {
        output += '<ul>'

        isListStarted = true
      }

      output += '<li>' + contentLine.substr(1) + '</li>'
    }

    whatToExpectHtml = output

    return { query, slug, userProfile, whatToExpectHtml }
  },
  data() {
    return {
      iconComponents: {
        'career-icon': CareerGradientIcon,
        'education-icon': EducationGradientIcon,
        'life-icon': LifeGradientIcon,
      },
      appEl: null,
      currentTime: this.$dayjs(),
      isShownMoreButton: false,
      showAllQualifications: false,
      isShownTimePickerDialog: false,
      isShownSummaryLessonDialog: false,
      isShownAvatarDialog: false,
      isShownMessageDialog: false,
      reviewsCarouselSettings: {
        dots: true,
        arrows: false,
        focusOnSelect: true,
        infinite: false,
        speed: 800,
        slidesToShow: 1,
        slidesToScroll: 1,
      },
      openCourses: [],
      coursesToShow: [],
      quantityCoursesToShowByDefault: 3,
      avatarIsLoaded: false,
      viewportWidth: 0,
      isSticky: false,
    }
  },
  head() {
    return {
      title: this.$t('user_profile_page.seo_title', {
        name: this.fullName,
        language: this.userProfile.languagesTaught
          .map((el) => el.name)
          .join(' & '),
      }),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.userProfile.shortSummary,
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('user_profile_page.seo_title', {
            name: this.fullName,
            language: this.userProfile.languagesTaught
              .map((el) => el.name)
              .join(' & '),
          }),
        },
        {
          property: 'og:description',
          content: this.userProfile.shortSummary,
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: this.userProfile.mainAvatar,
        },
        { hid: 'og:image:width', property: 'og:image:width', content: 600 },
        { hid: 'og:image:height', property: 'og:image:height', content: 600 },
        {
          hid: 'og:image:type',
          property: 'og:image:type',
          content: 'image/png',
        },
      ],
      bodyAttrs: {
        class: `${this.locale} teacher-profile-page`,
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    hasFreeSlots() {
      return this.userProfile.hasFreeSlots
    },
    acceptNewStudents() {
      return this.userProfile.acceptNewStudents
    },
    factsAboutTeacher() {
      return this.userProfile.factsAboutTeacher?.filter((item) => !!item) ?? []
    },
    reviews() {
      return this.$store.state.teacher_profile.reviews
    },
    qualifications() {
      const qualifications = this.userProfile.qualifications

      if (!this.showAllQualifications) {
        return qualifications.slice(0, 3)
      }

      return qualifications
    },
    trialPackage() {
      return this.$store.getters['teacher_profile/trialPackage']
    },
    isSelectedTrial() {
      return this.$store.state.teacher_profile.isSelectedTrial
    },
    lessonLengthPackages() {
      return this.$store.getters['teacher_profile/lessonLengthPackages']
    },
    courses() {
      return this.$store.getters['teacher_profile/courses'].map((item) => ({
        ...item,
        slug: getSlugByString(item.name),
      }))
    },
    isSelectedDefaultCourse() {
      return this.$store.getters['teacher_profile/isSelectedDefaultCourse']
    },
    selectedSlots() {
      return this.$store.state.teacher_profile.selectedSlots
    },
    feedbackTags() {
      return this.userProfile?.feedbackTagData ?? []
    },
    hasfeedbackTags() {
      return !!this.feedbackTags.length
    },
    firstName() {
      return this.userProfile.firstName?.trim()
    },
    lastName() {
      return this.userProfile.lastName?.trim()
    },
    fullName() {
      return `${this.firstName} ${this.lastName}`
    },
  },
  watch: {
    $route() {
      this.query = { ...this.$route.query }

      const { step } = this.query

      if (step) {
        if (step === 'schedule-lessons') {
          this.goToTimePicker()
        }

        if (step === 'lesson-summary') {
          this.goToSummaryLessonDialog()
        }

        if (this.appEl) {
          this.appEl.classList.add('modal-is-opened')
        }
      } else {
        this.isShownTimePickerDialog = false
        this.isShownSummaryLessonDialog = false

        if (this.appEl) {
          this.appEl.classList.remove('modal-is-opened')
        }
      }
    },
    isUserLogged(newValue, oldValue) {
      if (newValue) {
        this.$store
          .dispatch('teacher_profile/getServices', {
            slug: this.slug,
            currencyIsoCode: this.$store.getters['user/currency'].isoCode,
          })
          .then(() => this.init())
      }
    },
  },
  created() {
    this.isShownMoreButton =
      this.courses.length > this.quantityCoursesToShowByDefault &&
      this.courses.length !== this.coursesToShow.length
    this.coursesToShow =
      this.courses.length > this.quantityCoursesToShowByDefault
        ? this.courses.slice(0, this.quantityCoursesToShowByDefault)
        : this.courses
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll)
  },
  async beforeMount() {
    this.setViewportWidth()

    await this.removeStepQueryParam()

    this.appEl = document.getElementById('app')

    if (this.$refs.coursesText) {
      const el = this.$refs.coursesText?.querySelector('span')

      el.addEventListener(
        'click',
        () => {
          this.showTimePickerDialog()
        },
        null
      )
    }

    const hash = this.$route.hash?.replace('#', '')
    const courseIndex = this.courses.map((item) => item.slug).indexOf(hash)

    this.$nextTick(() => {
      if (courseIndex !== -1) {
        if (courseIndex >= this.quantityCoursesToShowByDefault) {
          this.showAllCourses(courseIndex)
        }

        this.goTo(hash)
        this.openCourses.push(courseIndex + 1)
      }
    })

    await this.init()
  },
  beforeDestroy() {
    this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS')
    this.$store.commit('teacher_profile/RESET_CURRENT_NUMBER_LESSON')

    // console.log('this.$cookies.get(L2SESSID)', this.$cookiz.get('L2SESSID'))
    if (this?.$cookiz?.get('L2SESSID')) {
      window.localStorage.removeItem('selected-slots')
      this.removeCourseFromLocalStorage()
      this.removeShowTimePickerFromLocalStorage()
    }
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    async init() {
      if (this.isUserLogged) {
        await this.$store.dispatch('loadingAllow', false)
        await Promise.all([
          this.$store.dispatch('purchase/getAdditionalCredits'),
          this.$store.dispatch('purchase/getPaymentMethods'),
        ])
        await this.$store.dispatch('loadingAllow', true)
      }

      const course = window.localStorage.getItem('current-course')
        ? JSON.parse(window.localStorage.getItem('current-course'))
        : null

      if (window.localStorage.getItem('current-course')) {
        if (course.isCourse) {
          await this.$store.dispatch(
            'teacher_profile/setSelectedCourse',
            course
          )
        } else {
          const selectedLessonLength = this.lessonLengthPackages.find(
            (item) =>
              !item.isTrial &&
              item.length === course.length &&
              item.packages.map((el) => el.id).includes(course.id)
          )

          if (selectedLessonLength) {
            this.$store.commit(
              'teacher_profile/SET_CURRENT_NUMBER_LESSON',
              course.lessons
            )
            await this.$store.dispatch(
              'teacher_profile/setSelectedLessonLength',
              selectedLessonLength
            )
          } else {
            this.selectCourseByDefault()
          }
        }

        this.removeCourseFromLocalStorage()
      } else {
        this.selectCourseByDefault()
      }

      if (window.localStorage.getItem('show-time-picker')) {
        const { currentTime } = JSON.parse(
          window.localStorage.getItem('show-time-picker')
        )

        if (currentTime) {
          this.currentTime = this.$dayjs(currentTime)

          this.$store
            .dispatch('teacher_profile/getSlots', {
              slug: this.slug,
              date: this.currentTime.day(1),
            })
            .then(() => {
              this.$nextTick(() => {
                this.showTimePickerDialog()
                this.removeShowTimePickerFromLocalStorage()
              })
            })
        }
      }
    },
    setViewportWidth() {
      this.viewportWidth = window.innerWidth
    },
    selectCourseByDefault() {
      const [selectedLessonLength] = this.lessonLengthPackages.filter(
        (item) => {
          if (!item.isCourse) {
            return this.trialPackage.lessons ? item.isTrial : item.length === 60
          }

          return false
        }
      )

      this.$store.dispatch(
        'teacher_profile/setSelectedLessonLength',
        selectedLessonLength
      )
    },
    showTimePickerDialog() {
      this.$router.push({
        path: this.$route.path,
        query: { ...this.query, step: 'schedule-lessons' },
      })
    },
    async closeTimePickerDialog() {
      if (!this.isSelectedDefaultCourse) {
        this.selectCourseByDefault()
      }

      await this.removeStepQueryParam()
    },
    async closeSummaryDialog() {
      if (!this.isSelectedDefaultCourse) {
        this.selectCourseByDefault()
      }

      await this.removeStepQueryParam()
    },
    removeStepQueryParam() {
      return new Promise((resolve) => {
        if (this.query?.step) {
          delete this.query.step

          this.$router.replace({ path: this.$route.path, query: this.query })
        }

        setTimeout(() => {
          resolve()
        })
      })
    },
    goToSummaryLessonDialog() {
      if (!this.isUserLogged) {
        // Save the current course and selected slots to localStorage
        if (!this.isSelectedTrial) {
          window.localStorage.setItem(
            'current-course',
            JSON.stringify(this.$store.state.teacher_profile.selectedCourse)
          )
        }

        if (this.selectedSlots.length) {
          window.localStorage.setItem(
            'selected-slots',
            JSON.stringify(this.selectedSlots)
          )
        }

        window.localStorage.setItem(
          'show-time-picker',
          JSON.stringify({
            currentTime: this.currentTime.format(),
          })
        )

        // Save the navigation state before opening login sidebar
        saveNavigationState()

        this.$store.commit('SET_IS_LOGIN_SIDEBAR', true)

        return
      }

      this.$router.push({
        path: this.$route.path,
        query: { ...this.query, step: 'lesson-summary' },
      })

      this.isShownTimePickerDialog = false
      this.isShownSummaryLessonDialog = true
    },
    goToTimePicker() {
      this.isShownSummaryLessonDialog = false
      this.isShownTimePickerDialog = true
    },
    toggleShowCourse(value) {
      if (this.openCourses.includes(value)) {
        this.openCourses = this.openCourses.filter((item) => item !== value)
      } else {
        this.openCourses.push(value)
      }
    },
    shownAvatarDialog() {
      if (this.userProfile?.avatars?.user_thumb_140x140) {
        this.isShownAvatarDialog = true
      }
    },
    goToCourse(slug) {
      this.addHashToPath(slug)
      this.goTo(slug)
    },
    addHashToPath(slug) {
      window.history.pushState(null, null, '#' + slug)
    },
    goTo(nodeClass) {
      const el = document.getElementById(nodeClass)

      if (el) {
        this.$vuetify.goTo(el, {
          duration: 300,
          offset: 15,
          easing: 'easeOutCubic',
        })
      }
    },
    removeCourseFromLocalStorage() {
      window.localStorage.removeItem('current-course')
    },
    removeShowTimePickerFromLocalStorage() {
      window.localStorage.removeItem('show-time-picker')
    },
    showAllCourses(index) {
      this.isShownMoreButton = false
      this.coursesToShow = this.courses

      this.$nextTick(() => {
        this.goTo(getSlugByString(this.courses[index].name))
      })
    },
    showMessageDialog() {
      this.$store
        .dispatch('message/checkConversation', this.userProfile.id)
        .then((res) => {
          if (res.threadId) {
            this.$store.dispatch('loadingStart')
            this.$router.push({ path: `/user/messages/${res.threadId}/view` })
          } else {
            this.isShownMessageDialog = true
          }
        })
    },
    avatarLoaded() {
      if (this.userProfile.mainAvatar) {
        const height = `${this.$refs.usersMainImage.naturalHeight}px`

        setStyleVariable('--height-user-avatar', height, '.avatar-dialog')
      }
    },
    handleScroll() {
      const triggerPoint = 10 // Adjust the scroll threshold
      this.isSticky = window.scrollY > triggerPoint
    },
  },
}
</script>

<style scoped lang="scss">
@import './assets/styles/teacher-profile.scss';
</style>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.teacher-profile {
  &-panel {
    background-color: #fff;
    box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1);
    border-radius: 20px;
  }

  .steps-wrap {
    @media #{map-get($display-breakpoints, 'sm-only')} {
      width: calc(100% + 50px);
    }
  }

  ul:not(.slick-dots) {
    margin: 8px 0;
    padding-left: 32px;

    li {
      &::marker {
        color: var(--v-success-base);
        background: linear-gradient(
            97.6deg,
            var(--v-success-base),
            var(--v-primary-base)
          ),
          #c4c4c4;
        background: -moz-linear-gradient(
            97.6deg,
            var(--v-success-base),
            var(--v-primary-base)
          ),
          #c4c4c4;
        background: -webkit-linear-gradient(
            97.6deg,
            var(--v-success-base),
            var(--v-primary-base)
          ),
          #c4c4c4;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      p {
        margin-bottom: 0 !important;
      }
    }
  }

  teacher-profile-sidebar-sticky {
    position: static;
    // transition: position 0.8s, top 0.8s;
  }

  .teacher-profile-sidebar-sticky.makeSticky {
    position: sticky;
    top: 84px;
  }

  .courses-text {
    span {
      position: relative;
      text-decoration: underline;
      cursor: pointer;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 1px;
        background: linear-gradient(
            90deg,
            var(--v-success-base) 3.04%,
            var(--v-primary-base) 27.45%
          ),
          #c4c4c4;
      }
    }
  }
}

.avatar-dialog {
  --height-user-avatar: 100%;

  display: flex;
  width: auto !important;
  height: var(--height-user-avatar) !important;

  .v-card {
    padding: 40px 0 0 !important;
  }

  .dialog-content {
    display: flex;
    height: 100%;
    justify-content: center;
    align-items: center;
  }

  .dialog-close {
    top: 8px;
    right: 8px;
  }

  img {
    display: block;
    width: auto;
    max-width: 100%;
    height: auto;
    max-height: 100%;
  }
}

.reviews {
  .slick-dots {
    margin-top: 20px;
    padding-right: 15px;

    li {
      margin: 8px 4px;

      button {
        width: 8px;
        height: 8px;
      }

      &.slick-active button {
        width: 12px;
        height: 12px;
      }
    }
  }
}
</style>

<template>
  <v-col class="col-12 px-0">
    <div
      :class="[
        'user-lessons',
        `user-lessons--${isTeacher ? 'teacher' : 'student'}`,
      ]"
    >
      <v-container fluid class="pa-0">
        <v-row no-gutters>
          <v-col class="col-12">
            <div class="user-lessons-wrap mx-auto">
              <div class="user-lessons-header mb-2 mb-md-4 mb-lg-5">
                <div class="user-lessons-title">
                  <h1 class="font-weight-medium">{{ $t('my_lessons') }} 📚</h1>
                  <div
                    v-if="isStudent && userCredit"
                    class="user-lessons-credits text-no-wrap"
                  >
                    {{ $t('langu_credit') }}: {{ currentCurrencySymbol
                    }}{{ getPrice(userCredit) }}
                  </div>
                </div>
                <div class="user-lessons-controls d-flex">
                  <div class="user-lessons-search">
                    <search-input
                      v-model.trim="searchQuery"
                      :placeholder="
                        isTeacher ? 'search_for_student' : 'search_for_teacher'
                      "
                      class="search-input--inner-border"
                      @submit="searchSubmitHandler"
                    ></search-input>
                  </div>
                  <div class="user-lessons-nav d-flex">
                    <v-btn
                      :to="localePath({ path: '/user/lessons' })"
                      class="nav-btn font-weight-medium"
                      height="48"
                    >
                      {{ $t('upcoming_lessons') }}
                    </v-btn>
                    <v-btn
                      :to="localePath({ path: '/user/past-lessons' })"
                      class="nav-btn font-weight-medium"
                      height="48"
                    >
                      {{ $t('past_lessons') }}
                    </v-btn>
                    <v-btn
                      :to="localePath({ path: '/user/unscheduled-lessons' })"
                      class="nav-btn font-weight-medium"
                      height="48"
                    >
                      {{ $t('unscheduled_lessons') }} ({{
                        totalNumberUnscheduledLessons
                      }})
                    </v-btn>
                  </div>
                </div>
              </div>
              <div class="user-lessons-body">
                <div class="user-lessons-content">
                  <div>
                    <div class="user-lessons-filters d-flex">
                      <l-chip
                        v-if="$route.query.search"
                        :label="$route.query.search"
                        light
                        class="mb-1 mr-1"
                        @click:close="resetSearch"
                      ></l-chip>
                      <l-chip
                        v-if="selectedDate"
                        :label="
                          $dayjs(selectedDate, 'YYYY-MM-DD').format('D MMMM')
                        "
                        light
                        class="mb-1 mr-1"
                        @click:close="resetSelectedDate"
                      ></l-chip>
                    </div>

                    <template v-if="lessons.length">
                      <div class="lessons-list">
                        <template v-for="lesson in lessons">
                          <component
                            :is="lessonComponents[lesson.type]"
                            :key="lesson.id"
                            v-bind="{ item: lesson, userStatuses }"
                          ></component>
                        </template>
                      </div>
                    </template>
                    <template v-else>
                      <div v-if="isStudent" class="lessons-list-empty">
                        <steps
                          :active-item-id="0"
                          :item-link="{ id: 1, path: '/teacher-listing' }"
                        ></steps>
                      </div>
                    </template>
                  </div>

                  <div v-if="totalPages > 1" class="mt-3 mt-md-5 text-center">
                    <pagination
                      :current-page="page"
                      :total-pages="totalPages"
                      :route="route"
                    ></pagination>
                  </div>
                </div>

                <aside
                  v-if="$vuetify.breakpoint.mdAndUp"
                  class="user-lessons-sidebar d-none d-md-block"
                >
                  <div class="user-lessons-sidebar-helper">
                    <calendar
                      v-for="i in 4"
                      :key="i"
                      :type="type"
                      :items="calendarItems"
                      :current-date="$dayjs().add(i - 1, 'month')"
                      @click-date="clickDate"
                    ></calendar>
                  </div>
                </aside>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </v-col>
</template>

<script>
import { hashUserData } from '@/utils/hash'
import { getPrice } from '~/helpers'

import Avatars from '~/mixins/Avatars'
import StatusOnline from '~/mixins/StatusOnline'
import SearchInput from '~/components/form/SearchInput'
import Calendar from '~/components/Calendar'
import UpcomingLesson from '~/components/user-lessons/UpcomingLesson'
import PastLesson from '~/components/user-lessons/PastLesson'
import UnscheduledLesson from '~/components/user-lessons/UnscheduledLesson'
import Steps from '~/components/Steps'
import Pagination from '~/components/Pagination'
import LChip from '~/components/LChip'

export default {
  name: 'LessonsPage',
  components: {
    SearchInput,
    Calendar,
    Steps,
    Pagination,
    LChip,
  },
  mixins: [Avatars, StatusOnline],
  props: {
    page: {
      type: Number,
      default: 1,
    },
    route: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      getPrice,
      lessonComponents: {
        upcoming: UpcomingLesson,
        past: PastLesson,
        unscheduled: UnscheduledLesson,
      },
      searchQuery: '',
      now: this.$dayjs(),
      selectedDate: null,
    }
  },
  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    lessons() {
      return this.$store.getters['lesson/lessons']
    },
    calendarItems() {
      return this.$store.state.lesson.calendarItems
    },
    totalPages() {
      return Math.ceil(
        this.$store.state.lesson.totalQuantity / process.env.NUXT_ENV_PER_PAGE
      )
    },
    totalNumberUnscheduledLessons() {
      return this.$store.getters['user/totalNumberUnscheduledLessons']
    },
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    userCredit() {
      return this.$store.getters['user/userCredit']
    },
  },
  watch: {
    selectedDate() {
      this.setArrStatusId()
    },
    '$route.params.search': {
      handler() {
        this.setArrStatusId()
      },
      deep: true,
    },
  },
  beforeMount() {
    this.searchQuery = this.$route.query?.search ?? ''

    // Check for paid trial event data
    if (localStorage.getItem('event_data') !== null) {
      try {
        const eventData = JSON.parse(localStorage.getItem('event_data'))
        if (eventData.event === 'purchase_paid_trial') {
          // Get user data
          const tidioData = this.$store.state.user.tidioData || {}
          const userData = this.$store.state.user.item || {}
          const userEmail = tidioData.email || ''
          const userName = `${userData.firstName || ''} ${
            userData.lastName || ''
          }`.trim()

          // Add hashed user data to the items array
          if (
            eventData.ecommerce &&
            eventData.ecommerce.items &&
            eventData.ecommerce.items.length > 0
          ) {
            eventData.ecommerce.items.forEach((item) => {
              if (!item.user_name) {
                item.user_name = hashUserData(userName)
              }
              if (!item.email_id) {
                item.email_id = hashUserData(userEmail)
              }
            })
          }

          // Ensure dataLayer is initialized
          window.dataLayer = window.dataLayer || []

          // Push event to dataLayer
          window.dataLayer.push({
            ecommerce: null,
          })
          window.dataLayer.push(eventData)

          // Remove event data from localStorage
          localStorage.removeItem('event_data')
        }
      } catch (error) {
        console.log(error)
      }
    }

    this.setArrStatusId()
    this.refreshStatusOnline()
  },
  methods: {
    setArrStatusId() {
      const prop = this.isTeacher ? 'studentId' : 'teacherId'

      this.arrStatusId = this.lessons.map((item) => item[prop])
    },
    async searchSubmitHandler() {
      if (this.selectedDate) {
        await this.resetSelectedDate()
      }

      await this.$router.push({
        name: this.$route.name,
        params: { page: '1' },
        query: this.searchQuery ? { search: this.searchQuery } : {},
      })
    },
    async clickDate(date) {
      if (this.searchQuery.length) {
        await this.resetSearch()
      }

      // if (this.type === 'past' || this.type === 'upcoming') {
      const data = { date }

      // if (this.type === 'past') data.type = 'past'
      if (this.type === 'upcoming') data.type = 'upcoming'

      this.$store
        .dispatch('lesson/getLessonsByDate', data)
        .then(() => (this.selectedDate = date))
      // }
    },
    resetSelectedDate() {
      return new Promise((resolve) => {
        const data = {
          page: this.page,
          perPage: process.env.NUXT_ENV_PER_PAGE,
          type: this.type,
        }

        this.$store.dispatch('lesson/getUpcomingLessons', data).then(() => {
          this.selectedDate = null

          resolve()
        })
      })
    },
    resetSearch() {
      return new Promise((resolve) => {
        this.searchQuery = ''

        this.$router.push({
          name: this.$route.name,
          params: { page: '1' },
          query: {},
        })

        setTimeout(() => {
          resolve()
        })
      })
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/user-lessons.scss';
</style>

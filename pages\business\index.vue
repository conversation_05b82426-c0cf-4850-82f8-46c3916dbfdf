<template>
  <business-page-component></business-page-component>
</template>

<script>
import BusinessPageComponent from '@/components/business-page/BusinessPage'

export default {
  name: 'BusinessPage',
  components: { BusinessPageComponent },
  async asyncData({ store }) {
    let pageData

    await store.dispatch('business_page/getPageData').then((res) => {
      pageData = res
    })

    return { pageData }
  },
  head() {
    console.log(this.$i18n.locale, this.pageData?.seoTitle, 'this.$i18n.locale')
    return {
      title:
        this.$i18n.locale === 'pl'
          ? 'Langu | Angielski biznesowy i nauka języków online dla firm'
          :  this.$i18n.locale === 'en' ? 'Langu | Business English & Online Language Learning for Companies'
          : 'Langu | Inglés de negocios y aprendizaje de idiomas en línea para empresas',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.pageData?.seoDescription ?? '',
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content:
            this.pageData?.seoTitle ??
            'Langu | Business English & Online Language Learning for Companies',
        },
        {
          property: 'og:description',
          content: this.pageData?.seoDescription ?? '',
        },
        { hid: 'og:image', property: 'og:image', content: this.previewImage },
        { hid: 'og:image:width', property: 'og:image:width', content: 772 },
        { hid: 'og:image:height', property: 'og:image:height', content: 564 },
        {
          hid: 'og:image:type',
          property: 'og:image:type',
          content: 'svg+xml',
        },
      ],
      bodyAttrs: {
        class: 'business-page',
      },
    }
  },
}
</script>

<template>
  <div class="banner">
    <div class="banner-content">
      <div v-if="banner.name" class="banner-title">
        <template v-if="banner.id">
          <template v-if="userTag">
            {{ banner.name }}
          </template>
          <template v-else>
            <template v-if="locale === 'es'">
              {{ selectedLanguage }} Para
              {{ banner.name }}
            </template>
            <template v-else-if="locale === 'pl'">
              {{ selectedLanguage }}: {{ banner.name.toLowerCase() }}
            </template>
            <template v-else>
              {{ selectedLanguage }} for
              {{ banner.name }}
            </template>
          </template>
        </template>
        <template v-else>
          {{ banner.name }}
        </template>
      </div>
      <div class="banner-text">
        {{ banner.description }}
      </div>
    </div>
    <div
      v-if="banner.image"
      :class="['banner-image d-flex', userTag ? 'align-center' : 'align-end']"
    >
      <div class="banner-image-helper">
        <v-img :src="banner.image" contain max-height="120" eager></v-img>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TeacherListingBanner',
  data() {
    return {
      banner: {},
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    activeFilters() {
      return this.$store.getters['teacher_filter/activeFilters']
    },
    selectedLanguage() {
      const language = this.$store.getters['teacher_filter/selectedLanguage']

      return language
        ? language.name.charAt(0).toUpperCase() + language.name.slice(1)
        : this.$t('learning')
    },
    selectedMotivation() {
      return this.activeFilters.find((item) => item.type === 'motivation')
    },
    selectedSpecialities() {
      return this.activeFilters.filter((item) => item.type === 'speciality')
    },
    motivationBanners() {
      return this.$store.state.teacher_filter.motivationBanners
    },
    specialityBanners() {
      return this.$store.state.teacher_filter.specialityBanners
    },
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    userTag() {
      return this.$store.getters['user/userTag']
    },
  },
  watch: {
    isUserLogged(newValue, oldValue) {
      if (newValue) {
        this.setBanner()
      }
    },
  },
  created() {
    this.setBanner()
  },
  methods: {
    setBanner() {
      let image = require('~/assets/images/banners/default.svg')
      if (this.userTag) {
        this.banner = {
          ...this.banner,
          name: this.userTag.headLine,
          description: this.userTag.description,
        }

        if (this.userTag?.logo && this.userTag?.logo.length > 0) {
          this.banner.image = this.userTag.logo
        }

        return
      }

      if (this.selectedMotivation) {
        const motivationBanner = this.motivationBanners.find(
          (item) => item.id === this.selectedMotivation.id
        )

        if (motivationBanner) {
          image = motivationBanner?.image
            ? require(`~/assets/images/banners/${motivationBanner.image}`)
            : image

          this.banner = {
            ...this.selectedMotivation,
            image,
            name: this.selectedMotivation.motivationName,
            description: this.$t(motivationBanner.description),
          }
        }

        if (this.selectedSpecialities.length === 1) {
          const speciality = this.selectedMotivation.specialities.find(
            (item) => item.id === this.selectedSpecialities[0].id
          )
          const specialityBanner = this.specialityBanners.find(
            (item) => item.id === speciality.id
          )

          if (speciality) {
            this.banner = {
              ...speciality,
              image: specialityBanner?.image
                ? require(`~/assets/images/banners/${specialityBanner.image}`)
                : image,
              props: specialityBanner?.props,
            }
          }
        }
        return
      }
      return (this.banner = { image })
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.banner {
  position: relative;
  display: flex;
  justify-content: space-between;
  min-height: 125px;
  padding: 8px 8px 0 32px;
  line-height: 1.333;

  @media #{map-get($display-breakpoints, 'md-only')} {
    padding: 5px 15px 0 20px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    flex-direction: column;
  }

  @media only screen and (max-width: $xsm-and-down) {
    padding: 16px 16px 0 16px;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // background: linear-gradient(97.6deg, #80b622 4.6%, #3c87f8 37.97%), #c4c4c4;
    opacity: 0.1;
    border-radius: 16px;
  }

  &-content {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 15px 10px 20px 0;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      max-width: 600px;
      min-width: 296px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      padding: 0 0 15px 0;
    }
  }

  &-title {
    margin-bottom: 8px;
    font-size: 24px;
    font-weight: 700;
    color: var(--v-success-base);
    background: linear-gradient(
      -75deg,
      var(--v-success-base),
      var(--v-primary-base)
    );
    background: -moz-linear-gradient(
      -75deg,
      var(--v-success-base),
      var(--v-primary-base)
    );
    background: -webkit-linear-gradient(
      -75deg,
      var(--v-success-base),
      var(--v-primary-base)
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    @media #{map-get($display-breakpoints, 'md-only')} {
      font-size: 22px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      font-size: 20px;
    }
  }

  &-text {
    font-weight: 300;
    font-size: 14px;
    letter-spacing: -0.002em;
  }

  &-image {
    @media #{map-get($display-breakpoints, 'xs-only')} {
      justify-content: center;

      .v-image {
        max-height: 90px !important;
      }
    }

    &-helper {
      width: 362px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        width: 250px;
      }
    }
  }
}
</style>

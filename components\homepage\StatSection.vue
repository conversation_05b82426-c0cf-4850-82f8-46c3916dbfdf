<template>
  <section class="stat">
    <v-container>
      <v-row>
        <v-col class="col-xl-10 offset-xl-1">
          <v-row>
            <v-col
              v-for="(item, idx) in items"
              :key="idx"
              :class="`col-12 col-sm-4 stat-item stat-item-i${idx + 1}`"
            >
              <div class="stat-item-wrap">
                <div class="stat-item-value text--gradient">
                  {{ item.value }}
                </div>
                <div class="stat-item-text" v-html="$t(item.text)"></div>
              </div>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-container>
  </section>
</template>

<script>
export default {
  name: 'StatSection',
  data() {
    return {
      items: [
        { value: '98%', text: 'home_page.stat_1' },
        { value: this.$t('n_15000') + '+', text: 'home_page.stat_2' },
        { value: '250+', text: 'home_page.stat_3' },
      ],
    }
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.stat {
  padding-top: 85px;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding-top: 40px;
  }

  @media only screen and (max-width: $xxs-and-down) {
    padding-top: 10px;
  }

  &-item {
    min-height: 138px;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      display: flex;
      justify-content: center;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      min-height: 172px;
    }

    &-wrap {
      position: relative;
      min-height: inherit;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        max-width: 258px;
        margin-left: auto;
        margin-right: auto;
      }

      &::before {
        content: '';
        position: absolute;
        top: 10px;
        left: -25px;
        width: 100%;
        height: 100%;
        background-size: contain;
        background-repeat: no-repeat;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          left: 0;
          top: -10px;
        }
      }
    }

    &-i1 {
      .stat-item-wrap::before {
        background-image: url('~assets/images/homepage/stat-1.svg');
        background-position: left center;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        .stat-item-text {
          max-width: 140px;
        }
      }
    }

    &-i2 {
      .stat-item-wrap::before {
        background-image: url('~assets/images/homepage/stat-2.svg');
        background-position: left center;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        .stat-item-text {
          max-width: 166px;
        }
      }
    }

    &-i3 {
      .stat-item-wrap::before {
        background-image: url('~assets/images/homepage/stat-3.svg');
        background-position: left center;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        .stat-item-text {
          max-width: 136px;
        }
      }
    }

    &-value {
      position: relative;
      margin-bottom: 12px;
      font-size: 64px;
      font-weight: 800;
      line-height: 1;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-bottom: 0;
        font-size: 56px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 54px;
        text-align: center;
      }
    }

    &-text {
      position: relative;
      font-size: 18px;
      color: var(--v-grey-base);
      line-height: 1.5;

      span {
        font-size: 16px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin: 0 auto;
        font-size: 15px;
      }
    }
  }
}
</style>

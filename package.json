{"name": "langu-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "lint:js": "eslint --ext \".js,.vue\" --ignore-path .gitignore .", "lint": "yarn lint:js", "lintfix": "eslint --fix --ext .js,.vue --ignore-path .gitignore ."}, "dependencies": {"@nuxt/types": "^2.15.4", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/dayjs": "^1.4.0", "@nuxtjs/google-gtag": "^1.0.4", "@nuxtjs/gtm": "^2.4.0", "@nuxtjs/pwa": "^3.3.5", "@nuxtjs/robots": "^2.5.0", "@nuxtjs/sentry": "^5.1.7", "@nuxtjs/sitemap": "^2.4.0", "@opentok/client": "2.21.2", "@stripe/stripe-js": "^3.4.1", "@tinymce/tinymce-vue": "^3.2.8", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tiptap/vue-2": "^2.11.5", "cookie-universal-nuxt": "^2.1.5", "core-js": "^3.8.3", "konva": "^7.0.6", "nuxt": "^2.15.4", "nuxt-facebook-pixel-module": "^1.6.0", "nuxt-i18n": "^6.28.1", "nuxt-stripe-module": "^3.2.0", "pdfjs-dist": "2.2.228", "plyr": "^3.6.12", "twilio-video": "^2.21.1", "uniqid": "^5.4.0", "vue-konva": "2.1.4", "vue-server-renderer": "2.7.16", "vue-slick-carousel": "^1.0.6", "vue-socket.io": "3.0.10", "vue-spinner": "^1.0.4", "vue-star-rating": "^1.7.0", "vuedraggable": "^2.24.3"}, "devDependencies": {"@mdi/js": "^7.2.96", "@nuxtjs/eslint-config": "^5.0.0", "@nuxtjs/eslint-module": "^3.0.2", "@nuxtjs/proxy": "^2.1.0", "@nuxtjs/vuetify": "^1.11.3", "babel-eslint": "^10.1.0", "eslint": "^7.18.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-nuxt": "^2.0.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.5.0", "prettier": "^2.2.1"}}
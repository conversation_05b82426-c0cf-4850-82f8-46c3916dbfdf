<template>
  <div :id="attachId" class="user-setting-select">
    <v-select
      :value="value"
      :items="items"
      height="44"
      full-width
      outlined
      dense
      :hide-selected="hideSelected"
      :hide-details="hideDetails"
      return-object
      :rules="rules"
      :placeholder="_placeholder"
      :item-value="itemValue"
      :item-text="itemText"
      :attach="`#${attachId}`"
      :validate-on-blur="validateOnBlur"
      :menu-props="{
        bottom: true,
        offsetY: true,
        minWidth: 200,
        maxHeight: 162,
        nudgeBottom: 8,
        contentClass: 'select-list l-scroll',
      }"
      v-on="$listeners"
    >
      <template #append>
        <svg width="12" height="12" viewBox="0 0 12 12">
          <use :xlink:href="chevronIcon"></use>
        </svg>
      </template>
      <template #item="{ item }">
        <div v-if="item.isoCode" class="icon">
          <v-img
            :src="require(`~/assets/images/flags/${item.isoCode}.svg`)"
            height="24"
            width="24"
            eager
          ></v-img>
        </div>
        <div class="text">{{ item[itemText] }}</div>
      </template>
    </v-select>
  </div>
</template>

<script>
export default {
  name: 'UserSettingSelect',
  props: {
    value: {
      type: Object,
      required: true,
    },
    items: {
      type: Array,
      required: true,
    },
    attachId: {
      type: String,
      required: true,
    },
    itemValue: {
      type: String,
      default: 'id',
    },
    itemText: {
      type: String,
      default: 'name',
    },
    hideDetails: {
      type: Boolean,
      default: true,
    },
    hideSelected: {
      type: Boolean,
      default: true,
    },
    rules: {
      type: Array,
      default: () => [],
    },
    validateOnBlur: {
      type: Boolean,
      default: false,
    },
    // eslint-disable-next-line vue/require-default-prop
    placeholder: [Boolean, String],
  },
  data: () => ({
    chevronIcon: `${require('~/assets/images/icon-sprite.svg')}#chevron-down`,
  }),
  computed: {
    _placeholder() {
      return !this.placeholder ? '' : this.$t(this.placeholder)
    },
  },
}
</script>

<style scoped lang="scss">
.user-setting-select {
  position: relative;
}
</style>

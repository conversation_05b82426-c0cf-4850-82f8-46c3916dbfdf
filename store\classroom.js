import { TOOL_POINTER } from '~/helpers/constants'

export const state = () => ({
  item: {},
  teacherId: null,
  studentId: null,
  users_joined_classroom: [],
  lessonId: null,
  userParams: {
    tool: TOOL_POINTER,
    cursor: 'cursor-pointer',
  },
  otherCursor: {
    username: null,
    tool: TOOL_POINTER,
    cursor: 'pointer',
    coords: {
      x: 0,
      y: 0,
    },
  },
  isVideoInputOpened: false,
  videoId: null,
  isLibraryOpened: false,
  newAssets: [],
  maxIndex: 5,
  assets: [],
  containerComponentEnabled: true,
  isDragging: false,
  toolNameBeforeChange: null,
  cursorNameBeforeChange: null,
  isScrollMainComponent: true,
  isCtrlKeyDown: false,
  isUserInteracted: false,
  acceptedFiles: {
    pdfTypes: ['pdf'],
    officeTypes: ['ppt', 'pptx', 'doc', 'docx', 'xls', 'xlsx'],
    imageTypes: ['png', 'jpg', 'jpeg', 'bmp', 'svg'],
    audioTypes: ['mp3', 'wav'],
  },
})

export const mutations = {
  SET_ITEM(state, value) {
    state.item = value
  },
  SET_LESSON_ID(state, id) {
    state.lessonId = id
  },
  SET_TEACHER_ID(state, id) {
    state.teacherId = id
  },
  SET_STUDENT_ID(state, id) {
    state.studentId = id
  },
  SET_IS_CTRL_KEY_DOWN(state, value) {
    state.isCtrlKeyDown = value
  },
  toggleVideoInput(state) {
    state.isVideoInputOpened = !state.isVideoInputOpened
  },
  closeVideoInput(state) {
    state.isVideoInputOpened = false
  },
  isDraggingTrigger(state, value) {
    state.isDragging = value
  },
  // updateAssets(state, v) {
  //   state.newAssets = v
  // },
  updateOtherCursor(state, data) {
    state.otherCursor = { ...state.otherCursor, ...data }
  },
  toggleLibrary(state) {
    state.isLibraryOpened = !state.isLibraryOpened
  },
  enableContainerComponent: (state, enabled) => {
    state.containerComponentEnabled = enabled
  },
  addAssets: (state, assets) => {
    state.assets = [...state.assets, ...assets]

    assets.forEach((asset) => {
      if (asset?.asset?.index > state.maxIndex) {
        state.maxIndex = asset.asset.index
      }
    })
  },
  setAssets: (state, assets) => {
    state.assets = assets

    assets.forEach((asset) => {
      if (asset?.asset?.index > state.maxIndex) {
        state.maxIndex = asset.asset.index
      }
    })
  },
  deleteAsset: (state, deleted) => {
    state.assets = state.assets.filter((asset) => asset.id !== deleted.id)
  },
  moveAsset: (state, moved) => {
    const item = state.assets.find((asset) => asset.id === moved.id)

    if (item) {
      item.asset = { ...item.asset, ...moved.asset }

      if (moved.asset.index && moved.asset.index > state.maxIndex) {
        state.maxIndex = moved.asset.index
      }
    }
  },
  updateAsset: (state, { id, asset }) => {
    const item = state.assets.find((asset) => asset.id === id)

    if (item) {
      item.asset = { ...item.asset, ...asset }
    }
  },
  setMaxIndex: (state, maxIndex) => {
    if (maxIndex > state.maxIndex) {
      state.maxIndex = maxIndex
    }
  },
  // setMouseMovePosition: (state, position) => {
  //   state.mouseMovePosition = position
  // },
  setUserTool: (state, tool) => {
    state.userParams.tool = tool
  },
  setUserCursor: (state, cursor) => {
    state.userParams.cursor = cursor
  },
  setToolNameBeforeChange: (state, payload) => {
    state.toolNameBeforeChange = payload
  },
  setCursorNameBeforeChange: (state, payload) => {
    state.cursorNameBeforeChange = payload
  },
  // setIsScrollMainComponent: (state, payload) => {
  //   state.isScrollMainComponent = payload
  // },
  setUsersJoinedClassroom: (state, payload) => {
    state.users_joined_classroom = payload
  },
  // UPDATE_CLASSROOM_STATUS: (state, payload) => {
  //   state.item.lessonStatus = payload
  // },
  USER_INTERACTED: (state) => {
    state.isUserInteracted = true
  },
}

export const getters = {
  userId: (state) =>
    state.item.userType === 'teacher' ? state.teacherId : state.studentId,
  // otherUserId: (state) =>
  //   state.item.userType !== 'teacher' ? state.teacherId : state.studentId,
  userName: (state) =>
    state.item.userType === 'teacher'
      ? state.item.teacherFirstName
      : state.item.studentFirstName,
  // otherUserName: (state) =>
  //   state.item.userType !== 'teacher'
  //     ? state.item.teacherFirstName
  //     : state.item.studentFirstName,
  role: (state) => state.item.userType,
  otherUserRole: (state) =>
    state.item.userType === 'teacher' ? 'student' : 'teacher',
  userParams: (state) => ({
    ...state.userParams,
    color: state.item.userType === 'teacher' ? '#7FB802' : '#3C87F8',
    role: state.item.userType,
  }),
  otherCursor: (state) => ({
    ...state.otherCursor,
    bgColorTooltip: state.item.userType === 'teacher' ? '#3C87F8' : '#7FB802',
  }),
  isOtherUserJoinedClassroom: (state) =>
    state.users_joined_classroom.includes(
      state.item.userType !== 'teacher' ? +state.teacherId : +state.studentId
    ),
  pdf: (state) => {
    return state.pdf
  },
  zoomAsset: (state) => {
    return state.assets.find(
      (asset) =>
        asset.asset.type === 'zoom' &&
        asset.asset.user_id ===
          (state.item.userType === 'teacher'
            ? state.teacherId
            : state.studentId)
    )
  },
  otherZoomAsset: (state) => {
    return (
      state.assets.find(
        (asset) =>
          asset.asset.type === 'zoom' &&
          asset.asset.user_id ===
            (state.item.userType !== 'teacher'
              ? state.teacherId
              : state.studentId)
      ) ?? {}
    )
  },
  audioAssets: (state) =>
    state.assets?.filter((asset) => asset.asset.type === 'audio') || [],
  tokboxApiKey: (state) => state.item?.tokBoxApiKey,
  tokboxSessionId: (state) => state.item?.tokBoxSessionId,
  tokboxToken: (state) => state.item?.tokBoxToken,
  twilioAccessToken: (state) => state.item?.twilioAccessToken,
  twilioRoomName: (state) => state.item?.roomName,
  // getToolbarAsset: state =>  {
  //   return state.assets.find(asset => asset.asset.type === 'toolbar' && asset.asset.user_id === (window.langu_role === 'teacher' ? window.teacher_id : window.student_id))
  // },
  // getOtherCursor: (state) => {
  //   return state.otherCursor
  // },
  isLocked: (state) =>
    state.assets.find((asset) => asset.asset.type === 'lock')?.asset?.isLocked,
  isLessonFinished: (state) => state.item.lessonStatus === 2,
  acceptedFilesStr: (state) => {
    const arr = []

    for (const prop in state.acceptedFiles) {
      arr.push(...state.acceptedFiles[prop].map((el) => '.' + el))
    }

    return arr.join(', ')
  },
}

export const actions = {
  getItem({ state, commit }, lessonId) {
    return this.$axios
      .get(
        `${process.env.NUXT_ENV_API_URL}/classroom/lesson-data/${lessonId}`,
        { progress: false }
      )
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_ITEM', data)

        return data
      })
  },
  getAssets({ state, commit }, lessonId) {
    return this.$axios
      .get(`${process.env.NUXT_ENV_API_URL}/classroom/${lessonId}`, {
        progress: false,
      })
      .then((res) => {
        commit('setAssets', res.data)
      })
  },
  createAsset({ state, commit }, asset) {
    return this.$axios
      .post(
        `${process.env.NUXT_ENV_API_URL}/classroom/${state.lessonId}`,
        asset,
        { progress: false }
      )
      .then((res) => {
        commit('addAssets', [res.data])
        this._vm.$socket.emit('asset-added', res.data)
      })
  },
  deleteAsset({ commit }, asset) {
    return this.$axios
      .delete(`${process.env.NUXT_ENV_API_URL}/classroom/${asset.id}`, {
        progress: false,
      })
      .then(() => {
        commit('deleteAsset', asset)
        this._vm.$socket.emit('asset-deleted', asset)
      })
  },
  moveAsset({ commit, dispatch }, asset) {
    dispatch('loadingAllow', false, { root: true })

    return this.$axios
      .put(`${process.env.NUXT_ENV_API_URL}/classroom/${asset.id}`, asset, {
        progress: false,
      })
      .then(() => {
        this._vm.$socket.emit('asset-moved', asset)
      })
      .finally(() => dispatch('loadingAllow', true, { root: true }))
  },
  updateAssetWithoutSync({ commit, dispatch }, asset) {
    dispatch('loadingAllow', false, { root: true })

    return this.$axios
      .put(`${process.env.NUXT_ENV_API_URL}/classroom/${asset.id}`, asset, {
        progress: false,
      })
      .finally(() => dispatch('loadingAllow', true, { root: true }))
  },
  uploadFiles({ state }, formData) {
    return this.$axios
      .post(
        `${process.env.NUXT_ENV_API_URL}/lesson/classroom/upload/canvas/${state.lessonId}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          progress: false,
        }
      )
      .then((response) => response.data)
  },
  getListOfFiles({ getters }, { formData, page }) {
    return this.$axios
      .post(
        `${process.env.NUXT_ENV_API_URL}/lesson/classroom/library/${getters.userId}/${page}`,
        formData,
        { progress: false }
      )
      .then((response) => response.data)
  },
  deleteFiles(ctx, data) {
    return this.$axios
      .post(
        `${process.env.NUXT_ENV_API_URL}/lesson/classroom/delete/library`,
        {
          data,
        },
        { progress: false }
      )
      .then((response) => response)
  },
  buzz({ dispatch }, lessonId) {
    dispatch('loadingAllow', false, { root: true })

    return this.$axios
      .get(`${process.env.NUXT_ENV_API_URL}/classroom/buzz/${lessonId}`, {
        progress: false,
      })
      .finally(() => dispatch('loadingAllow', true, { root: true }))
  },
  convertOfficeToPdf(ctx, file) {
    const formData = new FormData()

    formData.append('file', file)

    return this.$axios
      .post(`${process.env.NUXT_ENV_URL}/api/convert/office/to/pdf`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseType: 'blob',
        progress: false,
      })
      .then((response) => response.data)
      .then((data) => {
        const fileNameArr = file.name.split('.')

        fileNameArr.splice(-1, 1, 'pdf')

        return { data, fileName: fileNameArr.join('.') }
      })
  },
}

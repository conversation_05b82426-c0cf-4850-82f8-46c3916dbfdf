<template>
  <client-only>
    <stars-rating
      v-model="value"
      :read-only="readOnly"
      :show-rating="false"
      :star-size="16"
      inactive-color="transparent"
      active-color="#fbb03b"
      border-color="#fbb03b"
      :border-width="1"
      :padding="0"
      :star-points="[
        23, 2, 14, 17, 0, 19, 10, 34, 7, 50, 23, 43, 38, 50, 36, 34, 46, 19, 31,
        17,
      ]"
    ></stars-rating>
  </client-only>
</template>

<script>
export default {
  name: 'FormRate',
  props: {
    item: {
      type: Object,
      required: true,
    },
    property: {
      type: String,
      required: true,
    },
    readOnly: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    value: {
      get() {
        return this.item[this.property] || 0
      },
      set(value) {
        this.$emit('update-value', this.property, value)
      },
    },
  },
}
</script>

<style lang="scss">
.vue-star-rating-pointer {
  width: 16px;
  height: 16px;
  margin-right: 2px !important;
}
</style>

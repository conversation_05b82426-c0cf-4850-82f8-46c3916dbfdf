<script>
export default {
  computed: {
    role() {
      return this.$store.getters['classroom/role']
    },
  },
  methods: {
    setTool(toolName, cursorName) {
      this.$store.commit(
        'classroom/enableContainerComponent',
        toolName === 'pointer'
      )
      this.$socket.emit('cursor-moved', {
        tool: toolName,
        cursor: cursorName.replace(/(-cursor|cursor-)/i, ''),
        lessonId: this.$store.state.classroom.lessonId,
      })

      this.$store.commit('classroom/setUserTool', toolName)
      this.$store.commit('classroom/setUserCursor', cursorName)

      const el = document.body
      const classList = el.classList

      this.removeCursors(classList)
      el.classList.add(`${this.role}-${cursorName}`)

      this.classList = el.classList
    },
    removeCursors(classList) {
      classList.forEach((item) => {
        if (item.includes('cursor')) {
          document.body.classList.remove(item)
        }
      })
    },
  },
}
</script>

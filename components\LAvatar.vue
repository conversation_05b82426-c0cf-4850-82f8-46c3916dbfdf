<template>
  <div :class="['l-avatar', `l-avatar--${sizeClass}`]">
    <v-avatar
      :class="{
        'no-avatar': !clicked && !avatars[avatarSizes[0]],
      }"
      @click.stop="() => (clicked ? $emit('show-full-avatar') : false)"
    >
      <v-img
        :src="srcAvatar"
        :srcset="srcAvatarsSet"
        :options="{ rootMargin: '50%' }"
        :eager="eager"
      >
        <template #placeholder>
          <v-skeleton-loader type="avatar"></v-skeleton-loader>
        </template>
      </v-img>
    </v-avatar>
    <div v-if="languagesTaught.length" class="flags">
      <div
        v-for="language in languagesTaught"
        :key="language.isoCode"
        class="flags-item"
      >
        <v-img
          :src="require(`~/assets/images/flags/${language.isoCode}.svg`)"
          contain
          :options="{ rootMargin: '50%' }"
        ></v-img>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LAvatar',
  props: {
    avatars: {
      type: Object,
      required: true,
    },
    languagesTaught: {
      type: Array,
      required: true,
    },
    size: {
      type: String,
      default: 'lg',
    },
    eager: {
      type: Boolean,
      default: true,
    },
    defaultAvatar: {
      type: String,
      default: 'avatar.png',
    },
    clicked: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    sizeClass() {
      let size

      switch (this.size) {
        case 'md':
          size = 'medium'
          break
        case 'lg':
          size = 'large'
          break
        default:
          size = 'large'
      }

      return size
    },
    avatarSizes() {
      return Object.keys(this?.avatars?.avatarsResized)
    },
    srcAvatar() {
      const avatarFetchUrl =
        process.env.NUXT_ENV_NODE_ENV === 'development' ||
        process.env.NUXT_ENV_NODE_ENV === 'production'
          ? process?.env?.NUXT_ENV_URL ?? 'https://langu.io'
          : ''

      // Uncomment above code for pushing to staging and comment below line of code
      // const avatarFetchUrl = 'https://langu.io'

      // console.log('PhotoURL -> ', `${avatarFetchUrl + this?.avatars?.avatar}`)

      return this.avatars?.avatar
        ? `${avatarFetchUrl + this?.avatars?.avatar}`
        : this?.avatars && typeof this?.avatars === 'object'
        ? this.srcAvatarSingle
        : require(`~/assets/images/homepage/${this.defaultAvatar}`)
    },
    srcAvatarsSet() {
      let result = ''
      if (this?.avatars?.avatarsResized) {
        const avatSizes = Object?.keys(this?.avatars?.avatarsResized)
        for (let i = 0; i < avatSizes?.length; i++) {
          if (this.avatars?.avatarsResized[avatSizes[i]]) {
            result += `${this.avatars?.avatarsResized[avatSizes[i]]} ${i + 1}x`
            if (i < avatSizes?.length - 1) {
              result += ', '
            }
          }
        }
        // console.log('Result -> ', result)
      }
      return result?.length > 0 ? result : ''
    },
    srcAvatarSingle() {
      const keySet = Object.keys(this?.avatars)
      const resIndex = Math.ceil(keySet.length / 2)
      return this?.avatars[`${keySet[resIndex]}`] ?? ''
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.l-avatar {
  .flags {
    position: absolute;

    &-item {
      border-radius: 8px;
      filter: drop-shadow(2px 2px 12px rgba(146, 138, 138, 0.2));
      overflow: hidden;
    }
  }

  &--medium {
    --avatar-size: 96px;

    @media only screen and (max-width: $xxs-and-down) {
      --avatar-size: 74px;
    }

    .flags {
      right: 10px;
      top: 13px;

      @media only screen and (max-width: $mac-13-and-down) {
        top: 10px;
        right: 6px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        top: 6px;
        right: 10px;
      }

      &-item {
        margin-bottom: 6px;

        @media only screen and (max-width: $mac-13-and-down) {
          margin-bottom: 6px;
        }

        .v-image {
          width: 45px !important;
          height: 32px !important;

          @media only screen and (max-width: $mac-13-and-down) {
            width: 39px !important;
            height: 28px !important;
          }
        }
      }
    }
  }

  &--large {
    --avatar-size: 140px;

    @media only screen and (max-width: $mac-13-and-down) {
      --avatar-size: 120px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      --avatar-size: 80px;
    }

    width: 220px;

    @media only screen and (max-width: $mac-13-and-down) {
      width: 190px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 125px;
    }

    .flags {
      right: 32px;
      top: 16px;

      @media only screen and (max-width: $mac-13-and-down) {
        top: 12px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        top: 6px;
        right: 18px;
      }

      &-item {
        margin-bottom: 16px;

        .v-image {
          width: 62px !important;
          height: 44px !important;

          @media only screen and (max-width: $mac-13-and-down) {
            width: 50px !important;
            height: 38px !important;
          }

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            width: 35px !important;
            height: 26px !important;
          }
        }
      }
    }
  }

  .v-avatar {
    width: var(--avatar-size) !important;
    height: var(--avatar-size) !important;
    z-index: 2;

    &:not(.no-avatar) {
      cursor: pointer;
    }

    .v-skeleton-loader > div {
      width: var(--avatar-size) !important;
      height: var(--avatar-size) !important;
    }
  }
}
</style>

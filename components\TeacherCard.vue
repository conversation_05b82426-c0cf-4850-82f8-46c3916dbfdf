<template>
  <div class="teacher-card">
    <nuxt-link :to="link"></nuxt-link>
    <div class="teacher-card-top">
      <div class="teacher-card-avatar">
        <l-avatar
          class="teacher-card-avatar"
          :avatars="teacher"
          :avatars-resized="teacher.avatarsResized"
          :languages-taught="teacher.languagesTaught"
          size="md"
          :eager="false"
        ></l-avatar>
      </div>

      <div class="teacher-card-top-helper">
        <div class="teacher-card-name">
          {{ name }}
        </div>
        <div class="teacher-card-rating">
          <template v-if="teacher.averageRatings === 0">
            <div class="new-verified-teacher">
              <div class="new-verified-teacher-icon">
                <svg width="612" height="612" viewBox="0 0 612 612">
                  <use
                    :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#verified-user`"
                  ></use>
                </svg>
              </div>
              <span>{{ $t('new_verified_teacher') }}</span>
            </div>
          </template>
          <template v-else>
            <star-rating :value="teacher.averageRatings"></star-rating>

            <div class="review">
              ({{ $tc('review', teacher.countFeedbacks) }})
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="teacher-card-center">
      <div class="teacher-card-description">
        {{ teacher.description }}
      </div>
      <ul v-if="teacher.specialities.length" class="teacher-card-specialities">
        <li
          v-for="(specialization, index) in teacher.specialities.slice(0, 3)"
          :key="index"
        >
          <svg width="15" height="15" viewBox="0 0 15 15">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#${
                specialization.speciality.icon
              }`"
            ></use>
          </svg>
          {{ getTranslatedName(specialization.speciality) }}
        </li>
      </ul>
    </div>
    <div class="teacher-card-bottom">
      <div class="teacher-card-price">
        {{ $t('from') }}
        <span
          >{{ currentCurrencySymbol
          }}{{ getPrice(teacher.pricePerHourOfLesson) }}/</span
        >hr
      </div>
      <template v-if="teacher.acceptNewStudents && teacher.freeSlots">
        <template v-if="teacher.bookLesson.freeTrial">
          <v-btn :to="link" small color="success">
            {{ $t('free_trial') }}
          </v-btn>
        </template>
        <template v-else>
          <v-btn
            v-if="teacher.bookLesson.price"
            :to="link"
            small
            color="orange"
          >
            {{ $t('trial') }}:&#160; {{ currentCurrencySymbol
            }}{{ getPrice(teacher.bookLesson.price) }}
          </v-btn>
        </template>
      </template>
      <template v-else>
        <v-btn :to="link" small color="greyDark">
          {{ $t('full_schedule') }}
        </v-btn>
      </template>
    </div>
  </div>
</template>

<script>
import { getPrice } from '~/helpers'

import LAvatar from '~/components/LAvatar'
import StarRating from '~/components/StarRating'

export default {
  name: 'TeacherCard',
  components: { LAvatar, StarRating },
  filters: {
    specialitiesStr(arr) {
      let str = ''

      for (let i = 0; i < 3; i++) {
        str += arr[i]

        if (i < 3 - 1) {
          str += ', '
        }
      }

      return str
    },
  },
  props: {
    teacher: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      getPrice,
    }
  },
  computed: {
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    link() {
      return this.teacher.profileLink
    },
    name() {
      // Split the string into words by spaces and set first word as array element
      return [
        `${this.teacher.firstName?.split(' ')[0]?.toLowerCase()}`,
        `${this.teacher.lastName?.split(' ')[0]?.toLowerCase()}`,
      ]
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
        .join(' ') // Join the words back together with spaces
    },
  },
  methods: {
    getTranslatedName(speciality) {
      const currentLocale = this.$i18n.locale
      const translation = speciality.translations.find(
        (t) => t.locale === currentLocale && t.field === 'name'
      )
      return translation ? translation.content : speciality.name
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.teacher-card {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 20px 30px;
  box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1);
  border-radius: 20px;
  background-color: #fff;

  @media #{map-get($display-breakpoints, 'xs-only')} {
    max-width: 478px;
  }

  & > a {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    z-index: 3;
  }

  &-top {
    display: flex;

    &-helper {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      width: calc(100% - 140px);
      margin-bottom: 10px;
      padding: 12px 0;
      border-bottom: 1px solid #ecf3ff;

      @media only screen and (max-width: $mac-13-and-down) {
        width: calc(100% - 130px);
      }

      @media only screen and (max-width: $xxs-and-down) {
        width: calc(100% - 115px);
      }

      @media only screen and (max-width: 1099px) and (min-width: 991px),
        screen and (max-width: 799px) and (min-width: 767px),
        screen and (max-width: 439px) {
        flex-direction: column;
        align-items: flex-start;
      }
    }
  }

  &-center {
    display: flex;
    justify-content: space-between;
    padding: 15px 0 16px;

    @media only screen and (max-width: $mac-13-and-down) {
      flex-direction: column;
    }
  }

  &-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #ecf3ff;

    .v-btn {
      z-index: 4;
    }
  }

  &-name {
    padding-right: 20px;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.4;

    @media only screen and (max-width: $mac-13-and-down) {
      padding-right: 10px;
      font-size: 16px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding-right: 15px;
      font-size: 18px;
    }
  }

  &-rating {
    padding-top: 5px;

    @media only screen and (max-width: $mac-13-and-down) {
      padding-top: 3px;
    }

    .new-verified-teacher {
      position: relative;
      width: 112px;
      padding-left: 18px;
      font-size: 10px;
      font-weight: 500;
      text-align: left;

      @media only screen and (max-width: $mac-13-and-down) {
        width: 80px;
        font-size: 9px;
      }

      &-icon {
        position: absolute;
        left: 0;
        width: 16px;
        height: 16px;

        svg {
          width: 100%;
          height: 100%;
        }
      }
    }

    .review {
      margin-top: 5px;
      color: rgba(45, 45, 45, 0.7);
      font-size: 12px;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: 0.1px;
      text-align: right;

      @media only screen and (max-width: 1099px) and (min-width: 991px),
        screen and (max-width: 799px) and (min-width: 767px),
        screen and (max-width: 439px) {
        margin-top: 0;
        text-align: left;
      }
    }
  }

  &-description {
    width: calc(100% - 150px);
    font-weight: 400;
    font-size: 16px;
    line-height: 1.5;
    color: var(--v-dark-lighten3);

    @media only screen and (max-width: $mac-13-and-down) {
      width: 100%;
    }

    @media only screen and (max-width: $xxs-and-down) {
      font-size: 14px;
    }
  }

  &-specialities {
    width: 150px;
    padding-left: 0;
    font-size: 13px;
    font-weight: 300;
    list-style-type: none;

    @media only screen and (max-width: $mac-13-and-down) {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      margin-top: 16px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      width: 100%;
      margin-top: 16px;
    }

    li {
      position: relative;
      margin-bottom: 12px;
      padding-left: 40px;
      line-height: 1.15;

      @media only screen and (max-width: $mac-13-and-down) {
        width: 50%;
        padding: 0 15px 0 20px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-bottom: 10px;
      }

      &:last-child {
        margin-bottom: 0;
      }

      svg {
        position: absolute;
        left: 15px;
        top: -1px;

        @media only screen and (max-width: $mac-13-and-down) {
          left: 0;
        }
      }
    }
  }

  &-price {
    padding-right: 5px;
    font-size: 14px;

    span {
      font-size: 17px;
    }
  }
}

.teacher-card-specialities {
  font-size: 16px !important;
}
</style>

<style lang="scss">
@import './assets/styles/vars';

.teacher-card-avatar {
  position: relative;
  left: -4px;
  width: 140px;
  padding-right: 11px;

  @media only screen and (max-width: $mac-13-and-down) {
    width: 130px;
  }

  @media only screen and (max-width: $xxs-and-down) {
    width: 110px;
  }
}
</style>

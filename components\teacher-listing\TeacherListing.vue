<template>
  <v-col class="col-12 px-0">
    <div class="teacher-listing">
      <v-container fluid class="py-0">
        <v-row>
          <v-col class="col-12">
            <div class="teacher-listing-wrap">
              <div class="teacher-listing-content">
                <v-navigation-drawer
                  v-if="$vuetify.breakpoint.smAndDown"
                  id="teacher-filters"
                  v-model="drawer"
                  hide-overlay
                  fixed
                  color="darkLight"
                  width="375"
                >
                  <teacher-filter
                    @filters-loaded="filtersLoaded"
                  ></teacher-filter>
                </v-navigation-drawer>

                <!-- <div
                  class="filter-button d-md-none"
                  @click="openFilterClickHandler"
                >
                  {{ $t('filters') }}
                  <span v-if="quantityActiveFilters"
                    >({{ quantityActiveFilters }})</span
                  >
                  <div class="filter-button-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                      <use
                        :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#funnel`"
                      ></use>
                    </svg>
                  </div>
                </div> -->

                <div class="teacher-listing-header-title">
                  <div class="teacher-listing-header-title-text">
                    {{ $t('teacher_listing_header_title') }}
                  </div>
                  <span v-if="getBannerVisibility"
                    ><teacher-listing-banner></teacher-listing-banner
                  ></span>
                </div>

                <teacher-filter-new></teacher-filter-new>

                <teacher-listing-header></teacher-listing-header>

                <section v-if="teachers.length" class="teacher-listing-result">
                  <div class="teacher-listing-result-list">
                    <div
                      v-for="teacher in teachers"
                      :key="teacher.id"
                      class="teacher-listing-result-item"
                    >
                      <teacher-card :teacher="teacher"></teacher-card>
                    </div>
                  </div>

                  <div v-if="totalPages > 1" class="mt-3 mt-md-5 text-center">
                    <pagination
                      :current-page="page"
                      :total-pages="totalPages"
                      route="/teacher-listing"
                      :params="params"
                    ></pagination>
                  </div>
                </section>

                <div
                  v-if="!teachers.length && quantityActiveFilters"
                  class="teacher-listing-result--empty text-center"
                  v-html="
                    $t('empty_teacher_list', {
                      email: '<EMAIL>',
                    })
                  "
                ></div>
                <section
                  v-if="faqItems.length"
                  id="teacher-listing-page-faq-section"
                  ref="questions"
                  class="questions teacher-listing-page-faq-section"
                >
                  <div
                    v-if="backgroundImage"
                    ref="questionsSectionBg"
                    class="section-bg"
                  >
                    <v-img
                      :src="
                        require('~/assets/images/homepage/questions-bg.png')
                      "
                      position="center top"
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <v-container class="py-0 faq-custom-wrapper">
                    <v-row>
                      <v-col class="col-12 py-0">
                        <div class="section-head section-head--decorated">
                          <h3 :class="['section-head-title', titleClass]">
                            {{ $t('home_page.questions_section_title') }}
                          </h3>
                        </div>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col class="col-12 py-0">
                        <div class="questions-content">
                          <l-expansion-panels
                            :items="faqItems"
                            flat
                          ></l-expansion-panels>

                          <div class="questions-button">
                            <v-btn to="/faq" large color="primary">
                              {{ $t('see_our_full_faqs') }}
                            </v-btn>
                          </div>
                        </div>
                      </v-col>
                    </v-row>
                  </v-container>
                </section>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </v-col>
</template>

<script>
import TeacherFilterNew from '~/components/TeacherFilterNew'
import TeacherListingHeader from '~/components/teacher-listing/TeacherListingHeader'
import TeacherListingBanner from '~/components/teacher-listing/TeacherListingBanner'
import TeacherCard from '~/components/TeacherCard'
import Pagination from '~/components/Pagination'
import LExpansionPanels from '~/components/LExpansionPanels'

export default {
  name: 'TeacherListingPage',
  components: {
    TeacherFilterNew,
    TeacherListingHeader,
    TeacherListingBanner,
    TeacherCard,
    Pagination,
    LExpansionPanels,
  },
  props: {
    teachers: {
      type: Array,
      required: true,
    },
    faqItems: {
      type: Array,
      required: true,
    },
    page: {
      type: Number,
      default: 1,
    },
    params: {
      type: String,
      default: '',
    },
    items: {
      type: Array,
      required: true,
    },
    backgroundImage: {
      type: Boolean,
      default: false,
    },
    titleClass: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      scrollContainer: null,
      isCustomBreakpoint: false,
    }
  },
  computed: {
    quantityActiveFilters() {
      return this.$store.getters['teacher_filter/quantityActiveFilters']
    },
    totalPages() {
      return this.$store.getters['teacher/totalPages']
    },
    drawer: {
      get() {
        return this.$store.state.isShownTeacherFilter
      },
      set(value) {
        this.$store.commit('SET_IS_TEACHER_FILTER', value)
      },
    },
    breakpoint() {
      return this.isCustomBreakpoint
        ? this.$vuetify.breakpoint
        : { mdAndUp: true }
    },
    filterScroll: {
      get() {
        return window.sessionStorage.getItem('filter-scroll')
      },
      set(value) {
        window.sessionStorage.setItem('filter-scroll', value)
      },
    },
    getBannerVisibility() {
      return this.$vuetify.breakpoint.mdAndUp
    },
  },
  mounted() {
    this.scrollContainer = document
      ?.getElementById('teacher-filters')
      ?.getElementsByClassName('v-navigation-drawer__content')[0]
    this.isCustomBreakpoint = true

    if (this.scrollContainer) {
      this.$nextTick(() => {
        this.scrollContainer.addEventListener('scroll', this.onScroll)
      })
    }
  },
  beforeDestroy() {
    if (this.scrollContainer) {
      this.scrollContainer.removeEventListener('scroll', this.onScroll)
    }
  },
  methods: {
    openFilterClickHandler() {
      this.$store.commit('SET_IS_TEACHER_FILTER', true)
    },
    filtersLoaded() {
      if (
        this.$vuetify.breakpoint.smAndDown &&
        this.filterScroll &&
        this.scrollContainer
      ) {
        this.scrollContainer.scroll({
          top: this.filterScroll,
          behavior: 'instant',
        })
      }
    },
    onScroll() {
      this.filterScroll = this.scrollContainer.scrollTop
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.banner {
  position: relative;
  display: flex;
  justify-content: space-between;
  min-height: 125px;
  padding: 5px 8px 0 32px;
  line-height: 1.333;
  margin-top: 8px;

  @media #{map-get($display-breakpoints, 'md-only')} {
    padding: 5px 15px 0 20px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    flex-direction: column;
  }

  @media only screen and (max-width: $xsm-and-down) {
    padding: 16px 16px 0 16px;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // background: linear-gradient(97.6deg, #80b622 4.6%, #3c87f8 37.97%), #c4c4c4;
    opacity: 0.1;
    border-radius: 16px;
  }

  &-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 15px 10px 20px 0;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      max-width: 600px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      padding: 0 0 15px 0;
    }
  }

  &-title {
    margin-bottom: 8px;
    font-size: 24px;
    font-weight: 700;

    @media #{map-get($display-breakpoints, 'md-only')} {
      font-size: 22px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      font-size: 20px;
    }
  }

  &-text {
    font-weight: 300;
    font-size: 14px;
    letter-spacing: -0.002em;
  }

  &-image {
    display: flex;
    align-items: flex-end;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      justify-content: center;

      .v-image {
        max-height: 90px !important;
      }
    }

    &-helper {
      // width: 362px;
    }
  }
}

.teacher-listing-header-title {
  font-size: 36px;
  font-weight: 900;
  border-bottom: 1px #dadada solid;
  padding-bottom: 10px;
  display: flex;
  justify-content: space-between;
  place-items: flex-end;
}

.teacher-listing-header-title-text {
  min-width: 50%;
}

.teacher-listing-content {
  width: 100%;
}
</style>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';
.questions {
  position: relative;
  margin: 138px 0 82px;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    margin: 95px 0 82px;
  }

  .section-bg {
    top: 72px;
  }

  .section-head {
    margin-bottom: 118px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-bottom: 70px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-bottom: 40px;
    }
  }

  &-content {
    max-width: 920px;
    margin: 0 auto;

    @media only screen and (max-width: $xxs-and-down) {
      .v-expansion-panel-content__wrap {
        padding: 0 16px 20px !important;
      }
    }
  }

  &-button {
    display: flex;
    justify-content: center;
    margin-top: 45px;

    .v-btn {
      min-width: 202px !important;

      @media only screen and (max-width: $xxs-and-down) {
        min-width: 100% !important;
        width: 100% !important;
      }
    }
  }
}

.faq-custom-wrapper {
  display: grid;
  justify-content: center;
}

.section-head--decorated h3 {
  color: var(--v-success-base);
  background: -webkit-linear-gradient(
    -75deg,
    var(--v-success-base),
    var(--v-primary-base)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.teacher-listing-page-faq-section {
  padding-top: 50px;
  margin-top: 50px;
  padding-bottom: 70px;
}

.questions-content div div::before {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2),
    0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);
  border-bottom: 1px solid #dadada;
  border-radius: 0px !important;
}

.questions-content svg {
  fill: #ef5a6f !important;
}

.teacher-listing-page-faq-section h3 {
  color: var(--v-success-base);
  background: -webkit-linear-gradient(
    -75deg,
    var(--v-success-base),
    var(--v-primary-base)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.teacher-listing-page-faq-section
  .v-expansion-panels
  .v-expansion-panel::before {
  box-shadow: none !important;
}

.teacher-listing-page-faq-section .v-expansion-panels .v-expansion-panel {
  background-color: transparent !important;
  margin-bottom: 0px !important;
}
.teacher-listing-header-image {
  background-image: url('~/assets/images/banners/default.svg');
  width: 250px;
  height: 120px;
  background-position: center center;
}
</style>

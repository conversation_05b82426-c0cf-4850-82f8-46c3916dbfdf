<template>
  <div class="conversation">
    <div class="conversation-header mb-1">
      <div class="mr-1 mr-sm-2">
        <div class="conversation-title font-weight-medium">
          {{ $t('messages_with') }}
          <template v-if="!item.userIsDeleted">
            {{ item.firstName }}
            {{ item.lastName }}
          </template>
          <template v-else>
            {{ $t('deleted_user') }}
          </template>
        </div>
        <div v-if="!item.userIsDeleted" class="conversation-details mt-2">
          <div>
            {{ $t('next_lesson') }}:
            <span class="text-no-wrap">
              <template v-if="hasNextLesson">
                <span class="font-weight-medium">
                  {{
                    $dayjs(item.nextLessonDate)
                      .tz(userTimezone)
                      .format('ll, LT')
                  }}
                </span>
                (<nuxt-link :to="localePath({ path: '/user/lessons' })">{{
                  $t('see_lessons')
                }}</nuxt-link
                >)
              </template>
              <template v-else>
                <span class="font-weight-medium">
                  {{ $t('none_scheduled') }}
                </span>
              </template>
            </span>
          </div>
          <div>
            {{ $t('current_local_time_for') }}
            {{ item.firstName }}:
            <span class="text-no-wrap">
              <span class="font-weight-medium">
                {{ $dayjs().tz(item.recipientTimeZone).format('LT') }}
              </span>
              ({{ $dayjs().tz(item.recipientTimeZone).format('z') }})
            </span>
          </div>
          <div>
            {{ $t('last_online') }}:
            <span class="font-weight-medium text-no-wrap">
              <template v-if="status === 'online'">
                {{ $t('online_now') }}
              </template>
              <template v-else-if="status === 'idle'">
                {{ $t('online_but_idle') }}
              </template>
              <template v-else>
                {{
                  $dayjs(item.lastLoginDate).tz(userTimezone).format('ll, LT')
                }}
              </template>
            </span>
          </div>
        </div>
      </div>
      <div v-if="!item.userIsDeleted" class="conversation-avatar">
        <div>
          <v-avatar width="118" height="118">
            <v-img
              :src="getSrcAvatar(item.recipientAvatars, 'user_thumb_118x118')"
              :srcset="
                getSrcSetAvatar(
                  item.recipientAvatars,
                  'user_thumb_118x118',
                  'user_thumb_236x236'
                )
              "
              :options="{ rootMargin: '50%' }"
            ></v-img>
            <nuxt-link v-if="recipientLink" :to="recipientLink"></nuxt-link>
          </v-avatar>
          <user-status
            :user-id="item.userId"
            :user-statuses="userStatuses"
            large
          ></user-status>
        </div>
      </div>
    </div>
    <div class="conversation-body">
      <div v-if="!item.userIsDeleted" class="new-message">
        <div class="new-message-label font-weight-medium mb-1">
          {{ $t('write_new_message') }} 🖋️
        </div>
        <editor
          :value="newMessage"
          :limit="6000"
          auto-link
          @update="newMessage = $event"
          @validation="isMessageValid = $event"
          @submit="submitNewMessageHandler"
          @keydown="handleKeydown"
        ></editor>
        <div class="new-message-notice pl-2">
          {{ $t('press_ctrl_enter_to_send') }}
        </div>
        <div class="new-message-bottom">
          <div class="new-message-attached-file">
            <v-file-input
              ref="fileMessage"
              class="d-none"
              :rules="rules.file"
              prepend-icon=""
              @change="uploadFile"
            ></v-file-input>
            <!--            <template v-if="file">-->
            <!--              <div class="upload-file-name caption my-1 mx-2">-->
            <!--                {{ file.name }}-->
            <!--                <v-btn-->
            <!--                  class="file-remove-btn"-->
            <!--                  width="18"-->
            <!--                  height="18"-->
            <!--                  icon-->
            <!--                  @click="file = null"-->
            <!--                >-->
            <!--                  <v-img-->
            <!--                    :src="require('~/assets/images/close-gradient.svg')"-->
            <!--                    width="15"-->
            <!--                    height="15"-->
            <!--                  ></v-img>-->
            <!--                </v-btn>-->
            <!--              </div>-->
            <!--            </template>-->
          </div>
          <div class="new-message-bottom-buttons">
            <v-btn
              class="gradient font-weight-medium my-1 ml-1"
              @click="$refs.fileMessage.$el.querySelector('input').click()"
            >
              <div class="text--gradient">{{ $t('attach_document') }} 📎</div>
            </v-btn>
            <v-btn
              color="primary"
              class="font-weight-medium my-1 ml-1"
              @click="submitNewMessageHandler"
            >
              {{ $t('send') }} 📬
            </v-btn>
          </div>
        </div>
      </div>
      <div class="messages-list mt-2 mt-md-3">
        <conversation-item
          v-for="message in messages"
          :key="message.id"
          :item="message"
          :recipient-name="recipientName"
          :recipient-avatars="!item.userIsDeleted ? item.recipientAvatars : {}"
          :user-avatars="item.userAvatars"
          :thread-id="threadId"
          :user-statuses="userStatuses"
        ></conversation-item>
      </div>
      <div
        v-if="isMoreButtonShown"
        class="conversation-show-more-btn mt-2 mt-sm-5"
      >
        <load-more-btn
          :text-btn="$t('load_more_messages')"
          :fetch-func="fetchMessages"
        ></load-more-btn>
      </div>
    </div>
  </div>
</template>

<script>
import Editor from '~/components/form/Editor'
import LoadMoreBtn from '~/components/LoadMoreBtn'
import ConversationItem from '~/components/user-messages/ConversationItem'
import Avatars from '~/mixins/Avatars'
import UserStatus from '~/components/UserStatus'

export default {
  name: 'Conversation',
  components: { Editor, LoadMoreBtn, ConversationItem, UserStatus },
  mixins: [Avatars],
  props: {
    item: {
      type: Object,
      required: true,
    },
    userStatuses: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      newMessage: '',
      messagesPage: 1,
      isMessageValid: false,
      rules: {
        file: [
          (v) => !!v,
          (v) =>
            !v ||
            v.size < 10485760 ||
            this.$t('file_size_should_be_less_than', { value: '10 MB' }),
        ],
      },
    }
  },
  computed: {
    threadId() {
      return this.item.threadId
    },
    userTimezone() {
      return this.$store.getters['user/timeZone']
    },
    recipientName() {
      return !this.item.userIsDeleted
        ? this.item.firstName
        : this.$t('deleted_user')
    },
    recipientLink() {
      return this.item.username ? `/teacher/${this.item.username}` : null
    },
    hasNextLesson() {
      return this.item.nextLessonDate?.length
    },
    status() {
      let status = 'offline'

      if (
        Object.prototype.hasOwnProperty.call(
          this.userStatuses,
          this.item.userId?.toString()
        )
      ) {
        status = this.userStatuses[this.item.userId]
      }

      return status
    },
    totalPages() {
      return Math.ceil(
        this.item.countMessages / process.env.NUXT_ENV_MESSAGES_PER_PAGE
      )
    },
    messages() {
      return this.item.messages ?? []
    },
    isMoreButtonShown() {
      return this.totalPages > 1 && this.messagesPage < this.totalPages
    },
  },
  watch: {
    threadId() {
      this.messagesPage = 1
    },
  },
  beforeDestroy() {
    this.newMessage = ''
    this.file = null
  },
  methods: {
    uploadFile(file) {
      this.$store.dispatch('message/uploadFile', {
        threadId: this.threadId,
        file,
      })
    },
    submitNewMessageHandler() {
      if (this.isMessageValid) {
        this.$store
          .dispatch('message/sendMessage', {
            threadId: this.threadId,
            message: this.newMessage,
          })
          .then(() => {
            this.newMessage = ''
          })
      }
    },
    async fetchMessages() {
      this.messagesPage++

      await this.$store.dispatch('loadingAllow', false)
      await this.$store.dispatch('message/getConversation', {
        threadId: this.threadId,
        page: this.messagesPage,
      })
      await this.$store.dispatch('loadingAllow', true)
    },
    handleKeydown(event) {
      if (event.key === 'Enter' && !event.ctrlKey) {
        event.preventDefault()
        this.newMessage += '\n'
      } else if (event.key === 'Enter' && event.ctrlKey) {
        event.preventDefault()
        this.submitNewMessageHandler()
      }
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.conversation {
  padding: 30px 44px 138px;
  background-color: #fff;
  border-radius: 20px;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    padding: 24px 24px 60px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-up')} {
    box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1);
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    padding: 0;
  }

  &-header {
    display: flex;
    justify-content: space-between;

    .conversation {
      &-avatar {
        filter: drop-shadow(0px 4px 5px rgba(0, 0, 0, 0.2));

        & > div {
          position: relative;

          .v-avatar {
            position: relative;

            @media #{map-get($display-breakpoints, 'sm-and-down')} {
              width: 90px !important;
              height: 90px !important;
            }

            @media only screen and (max-width: $xsm-and-down) {
              width: 80px !important;
              height: 80px !important;
            }

            @media only screen and (max-width: $xxs-and-down) {
              width: 70px !important;
              height: 70px !important;
            }

            a {
              display: block;
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              z-index: 2;
            }
          }
        }

        .user-status {
          right: 3px;
          bottom: 3px;

          @media only screen and (max-width: $xsm-and-down) {
            right: 1px;
            bottom: 1px;
          }
        }
      }

      &-title {
        font-size: 24px;
        line-height: 1.333;

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          font-size: 20px;
        }

        @media #{map-get($display-breakpoints, 'xs-only')} {
          font-size: 18px;
        }
      }

      &-details {
        font-size: 13px;
        line-height: 1.23;
        color: var(--v-greyLight-darken2);

        @media #{map-get($display-breakpoints, 'xs-only')} {
          font-size: 12px;
        }

        & > div:not(:last-child) {
          @media #{map-get($display-breakpoints, 'md-and-down')} {
            margin-bottom: 4px;
          }
        }

        a {
          color: var(--v-grey-base);

          &:hover {
            color: var(--v-green-base);
            transition: all 0.3s;
          }
        }
      }
    }
  }

  &-body {
    .new-message {
      &-label {
        font-size: 16px;
      }

      .text-editor {
        .ProseMirror {
          min-height: 118px;
        }

        .text-editor-buttons {
          left: 8px;
          right: auto;
        }
      }

      &-notice {
        margin-bottom: 4px;
        font-size: 12px;
        color: var(--v-greyLight-darken2);
        line-height: 1.2;

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          margin-bottom: 8px;
        }
      }

      &-bottom {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;

        .upload-file-name {
          display: flex;
          align-items: center;
        }

        &-buttons {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-end;

          @media only screen and (max-width: $xxs-and-down) {
            justify-content: space-around;
          }

          .v-btn .v-btn__content {
            white-space: nowrap;
          }
        }
      }
    }

    .messages-list {
      display: flex;
      flex-direction: column;
    }

    .conversation-show-more-btn {
      max-width: 240px;
      margin-left: auto;
      margin-right: auto;
    }
  }
}
</style>
<template>
  <user-setting-template
    v-if="item"
    :title="$t('calendar')"
    :submit-func="submitData"
  >
    <v-row>
      <v-col class="col-12 col-md-8">
        <div class="input-wrap mb-3 mb-md-0">
          <div class="input-wrap-title body-1 font-weight-medium">
            {{
              $t(
                'do_you_want_to_automatically_add_lesson_bookings_to_your_calendar'
              )
            }}
          </div>
          <div
            v-if="$vuetify.breakpoint.mdAndUp"
            class="mt-4 d-none d-md-block"
            :style="styles"
          >
            <text-input
              v-model="email"
              type-class="border-gradient"
              height="44"
              :rules="emailRules"
              hide-details
              :placeholder="$t('calendar_email_address')"
            ></text-input>
          </div>
        </div>
      </v-col>
      <v-col class="col-12 col-md-auto" offset-md="1">
        <v-row align="center" justify="space-between">
          <v-col
            v-if="$vuetify.breakpoint.smAndDown"
            class="col-9 col-md-12 d-md-none"
          >
            <div :style="styles">
              <text-input
                v-model="email"
                type-class="border-gradient"
                height="44"
                :rules="emailRules"
                hide-details
                :placeholder="$t('calendar_email_address')"
              ></text-input>
            </div>
          </v-col>
          <v-col class="col-auto">
            <v-switch
              v-model="enabled"
              class="pt-0 mt-0 mt-md-3"
              inset
              :ripple="false"
              color="success"
              dense
              hide-details
            ></v-switch>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row v-if="isTeacher" class="pt-3 pt-md-4">
      <v-col class="col-12 col-md-8">
        <div class="input-wrap mb-3 mb-md-0">
          <div
            class="input-wrap-title body-1 font-weight-medium"
            v-html="$t('advanced_integration_google_calendar_only')"
          ></div>
        </div>
      </v-col>
      <v-col class="col-12 col-md-auto d-flex justify-end" offset-md="1">
        <v-switch
          v-model="syncCalendarWithSlots"
          class="pt-0 mt-0 mt-md-3"
          inset
          :ripple="false"
          color="success"
          dense
          hide-details
          @change="toggleAdvancedIntegration"
        ></v-switch>
      </v-col>
    </v-row>
  </user-setting-template>
</template>

<script>
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'
import TextInput from '@/components/form/TextInput'

export default {
  name: 'CalendarNotificationInfo',
  components: { UserSettingTemplate, TextInput },
  data() {
    return {
      syncCalendarWithSlots: undefined,
    }
  },
  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    item() {
      return this.$store.state.settings.notificationCalendarItem
    },
    emailRules() {
      return this.enabled ? [(v) => !!v, (v) => /.+@.+\..+/.test(v)] : []
    },
    email: {
      get() {
        return this.item.email
      },
      set(value) {
        this.$store.commit('settings/UPDATE_NOTIFICATION_CALENDAR_ITEM', {
          email: value,
        })
      },
    },
    enabled: {
      get() {
        return this.item.enabled
      },
      set(value) {
        this.$store.commit('settings/UPDATE_NOTIFICATION_CALENDAR_ITEM', {
          enabled: value,
        })
      },
    },
    // syncCalendarWithSlots() {
    //   console.log(this.$store.state.user.item.syncCalendarWithSlots)
    //   return this.$store.state.user.item.syncCalendarWithSlots
    // },
    styles() {
      let obj = {}

      if (!this.enabled) {
        obj = {
          opacity: 0,
          visibility: 'hidden',
          zIndex: -1,
        }
      }

      return obj
    },
  },
  beforeCreate() {
    this.$store.dispatch('settings/getNotificationCalendar')
  },
  beforeMount() {
    this.syncCalendarWithSlots = this.$store.state.user.item.syncCalendarWithSlots
  },
  methods: {
    submitData() {
      this.$store.dispatch('settings/updateNotificationCalendar')
    },
    toggleAdvancedIntegration(event) {
      window.location = event
        ? '/user/login/google-calendar'
        : '/google/calendar-notification-webhook-deactivate'
    },
  },
}
</script>

<style scoped lang="scss"></style>
